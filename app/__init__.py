from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from config import Config
from flask_caching import <PERSON><PERSON>
from flask_socketio import SocketIO

db = SQLAlchemy()

# Initialize cache outside of create_app so it can be imported by other modules
cache = Cache(config={'CACHE_TYPE': 'SimpleCache'})

# Initialize SocketIO outside of create_app so it can be imported by other modules
socketio = SocketIO()

# Make sure the news_service background thread is started after app is initialized
_news_thread_started = False

def create_app(config_name='default'):
    app = Flask(__name__)
    app.config.from_object(Config)

    # Set up logging to ensure all log messages are captured
    import logging
    from logging.handlers import RotatingFileHandler
    import os

    # Set the log level to DEBUG to capture all log messages
    app.logger.setLevel(logging.DEBUG)

    # Ensure logs are sent to stdout for Heroku
    stream_handler = logging.StreamHandler()
    stream_handler.setLevel(logging.DEBUG)
    app.logger.addHandler(stream_handler)

    app.logger.debug('Logging is set up with DEBUG level')

    # Security configurations for HTTPS
    app.config['SESSION_COOKIE_SECURE'] = True  # Only send cookies over HTTPS
    app.config['SESSION_COOKIE_HTTPONLY'] = True  # Prevent JavaScript from accessing cookies
    app.config['REMEMBER_COOKIE_SECURE'] = True  # Only send remember cookie over HTTPS
    app.config['PREFERRED_URL_SCHEME'] = 'https'  # Use HTTPS for url_for

    db.init_app(app)

    # Initialize cache with app
    cache.init_app(app)

    # Initialize SocketIO with app
    socketio.init_app(app, cors_allowed_origins="*", logger=True, engineio_logger=True)

    from app.routes import main
    app.register_blueprint(main)

    # Add security headers to every response
    @app.after_request
    def add_security_headers(response):
        # HTTP Strict Transport Security: instructs browsers to only use HTTPS
        response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        # Prevent clickjacking attacks
        response.headers['X-Frame-Options'] = 'SAMEORIGIN'
        # Help prevent XSS attacks
        response.headers['X-XSS-Protection'] = '1; mode=block'
        # Prevent MIME type sniffing
        response.headers['X-Content-Type-Options'] = 'nosniff'
        # Referrer policy
        response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        return response

    # Don't automatically create tables during app startup
    # Tables should be created using init_db.py script

    @app.before_request
    def setup_news_service_thread():
        """Initialize the news service background thread on first request."""
        global _news_thread_started

        if not _news_thread_started:
            try:
                from app.routes import news_service

                # Try to load cache from the database
                news_service._load_cache_from_db()

                # Start the background refresh thread if not already started
                news_service.start_background_refresh()

                # Mark thread as started
                _news_thread_started = True
                print("NewsService background thread started successfully")
            except Exception as e:
                print(f"Error starting NewsService background thread: {str(e)}")

    return app