import os
import json
import uuid
import hashlib
import time
import re
from openai import OpenAI
from dotenv import load_dotenv

load_dotenv()  # Load API key from .env file

class NewsSummarizer:
    def __init__(self):
        # Initialize the OpenAI client with base configuration
        # This version is compatible with OpenAI v1.66.3
        api_key = os.environ.get("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("OPENAI_API_KEY environment variable is not set")
        
        # Initialize the client with minimal configuration to avoid issues    
        self.client = OpenAI(
            api_key=api_key,
            # No additional parameters that might cause conflicts
        )
        
        # Always use GPT-3.5-turbo as specified
        self.model = "gpt-3.5-turbo"
        print(f"Using {self.model} for summarization")
        
        self.cache_dir = 'app/summarizer_cache'
        self.summary_cache = {}
        
        # Create cache directory if it doesn't exist
        try:
            if not os.path.exists(self.cache_dir):
                os.makedirs(self.cache_dir)
        except FileExistsError:
            # Directory already exists (possible race condition with multiple worker processes)
            pass
    
    def summarize(self, text, prompt_type="full", max_tokens=1024):
        """
        Generate a concise and well-structured summary of a news article.
        
        Args:
            text (str): The article content to summarize
            prompt_type (str): Type of prompt to use (full, basic) - defaults to full
            max_tokens (int): Maximum tokens for the response
            
        Returns:
            dict: The generated summary as a dictionary with 'summary' and 'key_points' keys
        """
        # Debug logging to see what content we're receiving
        print(f"DEBUG: Summarize called with text length: {len(text) if text else 0}")
        print(f"DEBUG: Text type: {type(text)}")
        if text:
            print(f"DEBUG: First 200 chars of text: {text[:200]}")
        else:
            print("DEBUG: Text is None or empty")
        
        # Check if article has enough content
        if not text or len(text) < 50:
            print(f"DEBUG: Content too short - returning error message. Length: {len(text) if text else 0}")
            return {
                "summary": "This article contains very limited content.",
                "key_points": [
                    "The article content is too brief to generate a meaningful summary.",
                    "Please check the original source for complete information."
                ]
            }
            
        # Force full prompt type for all summarizations
        prompt_type = "full"
            
        # Create a hash of the text and prompt type for caching
        cache_key = f"{prompt_type}_{hashlib.md5(text.encode()).hexdigest()}"
        
        # Check if we have this summary cached in memory
        if cache_key in self.summary_cache:
            print(f"Memory cache hit for summary: {cache_key[:10]}")
            cached_result = self.summary_cache[cache_key]
            return json.loads(cached_result) if isinstance(cached_result, str) else cached_result
        
        # Check if we have this summary cached on disk
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'r') as f:
                    cached_summary = f.read()
                    # Also store in memory for faster access next time
                    parsed_summary = json.loads(cached_summary)
                    self.summary_cache[cache_key] = parsed_summary
                    print(f"Disk cache hit for summary: {cache_key[:10]}")
                    return parsed_summary
            except Exception as e:
                print(f"Error reading from cache: {e}")
        
        # Truncate text if too long (to avoid token limits)
        max_chars = 15000
        if len(text) > max_chars:
            print(f"Truncating article from {len(text)} to {max_chars} characters")
            text = text[:max_chars] + "..."
        
        # Generate the summary based on prompt type - always using full
        try:
            # More detailed journalistic summary
            summary_data = self._generate_full_summary(text, max_tokens)
            
            # Cache the result in memory
            self.summary_cache[cache_key] = summary_data
            
            # Cache the result on disk
            with open(cache_file, 'w') as f:
                f.write(json.dumps(summary_data))
            
            return summary_data
            
        except Exception as e:
            print(f"Error in OpenAI summarization: {e}")
            # Provide a fallback summary
            return self.fallback_summarization(text)
    
    def summarize_from_title(self, title, max_tokens=500):
        """
        Generate a placeholder message when only the article title is available, 
        instead of generating potentially incorrect speculation.
        
        Args:
            title (str): The article title
            max_tokens (int): Maximum tokens for the response
            
        Returns:
            dict: A dictionary with summary and key points
        """
        # Create a simplified response with a disclaimer rather than speculating
        disclaimer_summary = (
            "Unable to access the full article content. To get an accurate summary, "
            "please click the 'Read Article' button to view the original article."
        )
        
        disclaimer_points = [
            "The article content could not be retrieved for summarization.",
            "This placeholder is shown instead of guessing about the content.",
            "For accurate information, please read the original article.",
            "If this persists, try refreshing the page or checking your internet connection."
        ]
        
        summary_data = {
            "summary": disclaimer_summary,
            "key_points": disclaimer_points
        }
        
        # Create a hash for caching
        cache_key = f"title_{hashlib.md5(title.encode()).hexdigest()}"
        
        # Cache the result
        self.summary_cache[cache_key] = summary_data
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")
        with open(cache_file, 'w') as f:
            f.write(json.dumps(summary_data))
            
        return summary_data
    
    def _generate_full_summary(self, text, max_tokens=1024):
        """Generate a detailed journalistic summary with the full model."""
        start = time.time()
        response = self.client.chat.completions.create(
            model=self.model,
            messages=[
                {"role": "system", "content": "You are an expert journalist and analyst specializing in concise news summarization. Your task is to analyze news articles and create clear, precise summaries that include key details, names, dates, statistics, and important context. Focus on accuracy, precision, and capturing the essential facts rather than oversimplification. CRITICAL: You must ONLY summarize the EXACT content provided and NEVER include information, dates, facts, or events that are not explicitly mentioned in the article text. Do not use your general knowledge or make assumptions about the content."}, 
                {"role": "user", "content": f"""Please provide a clear and brief summary of the following news article in 100 words at most. 

IMPORTANT: You must ONLY summarize the EXACT content provided. Do NOT include ANY information, names, dates, facts, or events that are not explicitly stated in the article. Do NOT use any external knowledge or make any assumptions about the content.

The summary should maintain journalistic standards by:
1. Including ONLY specific details like names, dates, locations, and statistics THAT APPEAR IN THE ARTICLE
2. Preserving important quotes from key figures THAT APPEAR IN THE ARTICLE
3. Capturing the nuance and complexity (not oversimplifying)
4. Explaining any specialized terminology USING ONLY INFORMATION FROM THE ARTICLE
5. Providing essential context THAT IS DIRECTLY MENTIONED IN THE ARTICLE

After the main summary, please identify 4-5 key points from the article that readers should take away. ONLY include points EXPLICITLY mentioned in the article.

Format your response as a JSON object with two keys: "summary" and "key_points" (which should be an array of individual points).

Here is the article:
{text}

Response Format:
{{
  "summary": "Your detailed 300-400 word summary here BASED SOLELY ON THE ARTICLE CONTENT...",
  "key_points": [
    "First key point with specific details FROM THE ARTICLE",
    "Second key point with specific details FROM THE ARTICLE",
    "Additional key points with specific details FROM THE ARTICLE..."
  ]
}}"""
                }
            ],
            temperature=0.7,
            max_tokens=max_tokens,
            response_format={"type": "json_object"}
        )
        duration = time.time() - start
        print(f"Full summary generation took {duration:.2f}s")
        
        # Process and clean the response
        return self._process_json_response(response.choices[0].message.content)
    
    def _generate_basic_summary(self, text, max_tokens=500):
        """Generate a basic summary with key points."""
        start = time.time()
        system_prompt = """You are an expert news analyst and editor who creates detailed and informative summaries.
        Your task is to analyze the provided news article and:
        
        1. Write a detailed 4-6 sentence summary that captures the main points and includes specific details
        such as names, dates, locations, statistics, and important context.
        
        2. Extract 4-5 detailed and specific key points from the article, ensuring each point includes concrete 
        facts and information.
        
        Format your response as a JSON object with 'summary' and 'key_points' fields.
        The 'key_points' should be an array of strings, with each string being a detailed bullet point."""
        
        response = self.client.chat.completions.create(
            model=self.model,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": text}
            ],
            max_tokens=max_tokens,
            temperature=0.5,
            response_format={"type": "json_object"}
        )
        duration = time.time() - start
        print(f"Basic summary generation took {duration:.2f}s")
        
        content = response.choices[0].message.content.strip()
        
        # Try to parse as JSON or extract information
        try:
            # First, clean up any potential markdown formatting
            cleaned_content = content
            
            # Remove markdown code blocks if present
            if "```" in cleaned_content:
                # Remove opening code block markers
                cleaned_content = re.sub(r'```(?:json)?\s*', '', cleaned_content)
                # Remove closing code block markers
                cleaned_content = re.sub(r'\s*```\s*', '', cleaned_content)
            
            # Try to parse as JSON
            summary_data = json.loads(cleaned_content)
            
            # Ensure expected keys exist with correct format
            summary_data = self._normalize_summary_data(summary_data)
            return summary_data
            
        except json.JSONDecodeError:
            # If not valid JSON, extract content manually
            return self._extract_from_text(content)
    
    def _process_json_response(self, content):
        """Process JSON response and ensure it has the expected format."""
        try:
            parsed = json.loads(content)
            return self._normalize_summary_data(parsed)
        except json.JSONDecodeError:
            # If we can't parse the JSON, return a simple fallback
            return {
                "summary": "The summarization model produced invalid output. Please try again later.",
                "key_points": [
                    "Summarization failed due to technical issues.",
                    "Please refer to the original article for information."
                ]
            }
    
    def _normalize_summary_data(self, data):
        """Ensure the summary data has the expected format and fields."""
        # Normalize the field names
        summary = data.get('summary', '') or data.get('article_summary', '')
        key_points = data.get('key_points', [])
        
        # Ensure key_points is a list
        if not isinstance(key_points, list):
            key_points = []
        
        # Clean key points
        cleaned_points = []
        for point in key_points:
            if isinstance(point, str):
                # Clean each key point, removing bullet markers if any
                clean_point = re.sub(r'^[\s•\-\*]+', '', point.strip())
                cleaned_points.append(clean_point)
        
        return {
            "summary": summary.strip(),
            "key_points": cleaned_points
        }
    
    def _extract_from_text(self, content):
        """Extract summary and key points from text when JSON parsing fails."""
        article_summary = ''
        key_points = []
        
        # Try to extract summary
        if "article_summary" in content.lower() or "summary" in content.lower():
            # Try to find the summary section
            summary_match = re.search(r'(?:article_summary"|"summary")\s*:\s*"([^"]+)"', content)
            if summary_match:
                article_summary = summary_match.group(1).strip()
            else:
                # Just use the first paragraph as summary
                paragraphs = re.split(r'\n\s*\n', content)
                if paragraphs:
                    article_summary = paragraphs[0].strip()
                    # Remove any potential header like "Summary:"
                    article_summary = re.sub(r'^(?:Summary|Article summary):?\s*', '', article_summary)
        
        # Look for key points
        if "key_points" in content.lower() or "key points" in content.lower():
            # Try to find all bullet points using multiple patterns
            bullet_matches = re.findall(r'(?:[-•*]\s*|\d+\.\s*)([^-•*\n][^\n]+)', content)
            if bullet_matches:
                key_points = [point.strip() for point in bullet_matches if point.strip()]
            else:
                # Try another approach for finding points
                lines = content.split('\n')
                for line in lines:
                    if re.match(r'^\s*(?:[-•*]|\d+\.)\s+', line):
                        point = re.sub(r'^\s*(?:[-•*]|\d+\.)\s+', '', line).strip()
                        if point:
                            key_points.append(point)
        
        return {
            "summary": article_summary,
            "key_points": key_points
        }
    
    def fallback_summarization(self, text):
        """Generate a fallback summary if the API call fails."""
        return {
            "summary": "Unable to generate a summary for this article. The content may be too complex or the summarization service encountered an error.",
            "key_points": [
                "Summary generation failed.",
                "Please check the original article for accurate information."
            ]
        }
    
    def generate_summary(self, text, title=None, max_tokens=700):
        """
        Generate a summary of the article content.
        This is a wrapper around the summarize method for compatibility.
        
        Args:
            text (str): The article content to summarize
            title (str, optional): The article title
            max_tokens (int): Maximum tokens for the response
            
        Returns:
            dict: The generated summary with 'article_summary' and 'key_points' keys
        """
        # Call the existing summarize method
        summary_result = self.summarize(text, prompt_type="full", max_tokens=max_tokens)
        
        # Convert the summary to article_summary for compatibility
        if 'summary' in summary_result and 'article_summary' not in summary_result:
            summary_result['article_summary'] = summary_result['summary']
            # Keep the summary key for backward compatibility
        
        return summary_result
    
    def extract_key_points(self, summary_text, max_points=5):
        """
        Extract key points from a summary text.
        
        Args:
            summary_text (str): The summary text to extract key points from
            max_points (int): Maximum number of key points to extract
            
        Returns:
            list: A list of key points
        """
        try:
            # Extract key points from the summary text
            key_points = []
            
            # Split the text into sentences and filter out short ones
            sentences = re.split(r'(?<=[.!?])\s+', summary_text)
            filtered_sentences = [s for s in sentences if len(s) > 20]
            
            # Extract up to max_points key points
            for i, sentence in enumerate(filtered_sentences):
                if i >= max_points:
                    break
                key_points.append(sentence.strip())
            
            return key_points
                
        except Exception as e:
            print(f"Error extracting key points: {e}")
            return []

    def extract_content(self, url, fetch_image=False):
        """
        Extract article content from a URL using newspaper3k.
        If newspaper3k fails, return empty content so routes.py can handle the fallback to NewsAPI description.
        
        Args:
            url (str): The URL to extract content from
            fetch_image (bool): Whether to also extract the main image URL
            
        Returns:
            str or dict: The extracted content (string) or content+image (dict) if fetch_image=True
        """
        if not url:
            if fetch_image:
                return {"content": "", "image_url": "", "extraction_method": "No URL provided"}
            else:
                return ""
        
        print(f"Extracting content from: {url}")
        
        # Method 1: Extract content using newspaper3k
        try:
            from newspaper import Article
            
            # Create article object
            article = Article(url)
            
            # Download and parse the article
            article.download()
            article.parse()
            
            # Get the content and title
            title = article.title
            text = article.text
            
            # Combine title and content
            content = f"{title}\n\n{text}" if title else text
            
            # Get the main image URL if requested
            image_url = ""
            if fetch_image and hasattr(article, 'top_image'):
                image_url = article.top_image or ""
            
            print(f"newspaper3k extraction: {len(content)} characters")
            
            # Check if we got sufficient content (at least 100 characters)
            if content and len(content) >= 100:
                # Return content or content+image based on fetch_image parameter
                if fetch_image:
                    return {
                        "content": content,
                        "image_url": image_url,
                        "extraction_method": "newspaper3k"
                    }
                else:
                    return content
            else:
                print("newspaper3k extraction returned insufficient content")
        except Exception as e:
            print(f"Error extracting content with newspaper3k: {e}")
        
        # If newspaper3k fails, return empty content so routes.py can handle the fallback
        print("Content extraction failed, returning empty content for API description fallback")
        if fetch_image:
            return {"content": "", "image_url": "", "extraction_method": "Failed"}
        else:
            return ""


def process_structured_summary(summary_json):
    """Process the structured summary from JSON string to Python dictionary."""
    try:
        return json.loads(summary_json)
    except:
        # Fallback for invalid JSON
        return {
            "summary": "Invalid summary format. Please check the original article.",
            "key_points": ["Error processing the summary."]
        } 