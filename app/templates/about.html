{% extends "base.html" %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">About News Summarizer</h1>
            <p class="lead">
                This application provides AI-powered summaries and context for news articles from reputable sources.
            </p>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h2 class="h4 mb-0">System Architecture</h2>
                </div>
                <div class="card-body">
                    <p>{{ technologies.system_architecture }}</p>
                    
                    <div class="text-center my-4">
                        <img src="{{ url_for('static', filename='img/architecture-diagram.png') }}" alt="System Architecture" class="img-fluid rounded" style="max-width: 100%; height: auto;">
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-light">
                                    <h4 class="mb-0">Initial Load (Fast)</h4>
                                </div>
                                <div class="card-body">
                                    <ol>
                                        <li>User selects a news topic</li>
                                        <li>App fetches headlines via NewsAPI</li>
                                        <li>Basic article metadata is displayed</li>
                                        <li>No summaries are generated yet</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-light">
                                    <h4 class="mb-0">On-Demand Processing</h4>
                                </div>
                                <div class="card-body">
                                    <ol>
                                        <li>User clicks "Summarize" on an article</li>
                                        <li>App fetches full content via Puppeteer browser engine</li>
                                        <li>JavaScript-rendered content is extracted for better results</li>
                                        <li>Content is processed by AI models</li>
                                        <li>Summary and key points are displayed</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <h2 class="mt-5 mb-3">API Technologies</h2>
            
            {% for tech in technologies.api_technologies %}
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="h5 mb-0">{{ tech.name }}</h3>
                </div>
                <div class="card-body">
                    <p><strong>Description:</strong> {{ tech.description }}</p>
                    <p><strong>Purpose:</strong> {{ tech.purpose }}</p>
                </div>
            </div>
            {% endfor %}
            
            <h2 class="mt-5 mb-3">AI Technologies</h2>
            
            {% for tech in technologies.ai_technologies %}
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="h5 mb-0">{{ tech.name }}</h3>
                </div>
                <div class="card-body">
                    <p><strong>Description:</strong> {{ tech.description }}</p>
                    <p><strong>Usage:</strong> {{ tech.usage }}</p>
                </div>
            </div>
            {% endfor %}
            
            <div class="text-center mt-5">
                <a href="{{ url_for('main.index') }}" class="btn btn-primary">Back to Home</a>
            </div>
        </div>
    </div>
</div>
{% endblock %} 