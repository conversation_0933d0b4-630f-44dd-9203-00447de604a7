{% extends "base.html" %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Admin Navigation</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('main.admin_dashboard') }}" class="list-group-item list-group-item-action">
                        <i class="bi bi-speedometer2 me-2"></i> Dashboard
                    </a>
                    <a href="{{ url_for('main.admin_users') }}" class="list-group-item list-group-item-action active">
                        <i class="bi bi-people me-2"></i> Users
                    </a>
                    <a href="{{ url_for('main.admin_logout') }}" class="list-group-item list-group-item-action text-danger">
                        <i class="bi bi-box-arrow-right me-2"></i> Logout
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-md-9">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Registered Users</h5>
                    <span class="badge bg-primary">{{ users|length }} Users</span>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Email</th>
                                    <th>Registered</th>
                                    <th>Last Visit</th>
                                    <th>Visit Count</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users %}
                                <tr>
                                    <td>{{ user.id }}</td>
                                    <td>{{ user.email }}</td>
                                    <td>{{ user.created_at.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ user.last_visit.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>{{ user.visit_count }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if pagination.pages > 1 %}
                    <nav aria-label="User pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <li class="page-item {% if pagination.page == 1 %}disabled{% endif %}">
                                <a class="page-link" href="{{ url_for('main.admin_users', page=pagination.page-1) }}" tabindex="-1">Previous</a>
                            </li>
                            
                            {% for p in range(1, pagination.pages + 1) %}
                            <li class="page-item {% if p == pagination.page %}active{% endif %}">
                                <a class="page-link" href="{{ url_for('main.admin_users', page=p) }}">{{ p }}</a>
                            </li>
                            {% endfor %}
                            
                            <li class="page-item {% if pagination.page == pagination.pages %}disabled{% endif %}">
                                <a class="page-link" href="{{ url_for('main.admin_users', page=pagination.page+1) }}">Next</a>
                            </li>
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
