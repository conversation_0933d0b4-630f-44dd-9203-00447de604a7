{% extends "base.html" %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Admin Navigation</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('main.admin_dashboard') }}" class="list-group-item list-group-item-action active">
                        <i class="bi bi-speedometer2 me-2"></i> Dashboard
                    </a>
                    <a href="{{ url_for('main.admin_users') }}" class="list-group-item list-group-item-action">
                        <i class="bi bi-people me-2"></i> Users
                    </a>
                    <a href="{{ url_for('main.admin_logout') }}" class="list-group-item list-group-item-action text-danger">
                        <i class="bi bi-box-arrow-right me-2"></i> Logout
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-md-9">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Dashboard Overview</h5>
                    <span class="badge bg-primary">{{ stats.total_users }} Total Users</span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card bg-light mb-3">
                                <div class="card-body text-center">
                                    <h3 class="display-4">{{ stats.total_users }}</h3>
                                    <p class="text-muted">Registered Users</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light mb-3">
                                <div class="card-body text-center">
                                    <h3 class="display-4">{{ stats.active_today }}</h3>
                                    <p class="text-muted">Active Today</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light mb-3">
                                <div class="card-body text-center">
                                    <h3 class="display-4">{{ stats.visits_today }}</h3>
                                    <p class="text-muted">Visits Today</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <h5 class="mt-4 mb-3">Recent Users</h5>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Email</th>
                                    <th>Registered</th>
                                    <th>Last Visit</th>
                                    <th>Visit Count</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in recent_users %}
                                <tr>
                                    <td>{{ user.email }}</td>
                                    <td>{{ user.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                    <td>{{ user.last_visit.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                    <td>{{ user.visit_count }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
