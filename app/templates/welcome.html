{% extends "base.html" %}

{% block head %}
{{ super() }}
<!-- Add reCAPTCHA script for invisible reCAPTCHA -->
<script src="https://www.google.com/recaptcha/api.js" async defer></script>
<script>
function onSubmit(token) {
    document.getElementById("email-form").submit();
}
</script>
{% endblock %}

{% block content %}
<div class="container py-5">

    <div class="row justify-content-center mt-5">
        <div class="col-md-8 col-lg-6">
            <div class="p-4 my-3">
                {% if error %}
                <div class="alert alert-danger" role="alert">
                    {{ error }}
                </div>
                {% endif %}

                <form id="email-form" method="POST" action="{{ url_for('main.register') }}">
                    <div class="mb-3">
                        <label for="email" class="form-label">Enter your email to access the app</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>

                    <div class="d-grid gap-2">
                        <button
                            type="button"
                            class="g-recaptcha btn btn-primary"
                            data-sitekey="{{ recaptcha_site_key }}"
                            data-callback="onSubmit"
                            data-size="invisible"
                        >
                            Continue
                        </button>
                    </div>

                    <div class="mt-3 text-center small text-muted">
                        By continuing, you agree to our <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">Terms of Use</a>.
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
