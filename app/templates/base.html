<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BIRDII: News with context, knowledge with every read</title>

    {% block head %}
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <!-- Google Fonts for NYT-style typography - with better character support -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Noto+Sans:ital,wght@0,400;0,500;0,700;1,400&display=swap">
    {% endblock %}

    <style>
        /* Improved font definitions for better character rendering */
        :root {
            /* Font families with better character support - all Sans Serif now */
            --font-cheltenham: 'Noto Sans', -apple-system, BlinkMacSystemFont, sans-serif; /* Headline font */
            --font-imperial: 'Noto Sans', -apple-system, BlinkMacSystemFont, sans-serif;   /* Body copy font */
            --font-franklin: 'Noto Sans', -apple-system, BlinkMacSystemFont, sans-serif;  /* UI elements, captions */

            /* Enforce proper entity encoding */
            --entity-encoding: strict;

            /* Colors */
            --color-black: #121212;
            --color-dark-gray: #333333; /* Keeping as recommended */
            --color-medium-gray: #666666;
            --color-light-gray: #f7f7f7;
            --color-off-white: #fafafa;
            --color-border: #e2e2e2;
            --color-link: #555555; /* Updated from teal to light dark */
            --color-link-hover: #444444; /* Darker light dark for hover */
            --color-accent: #00BFFF; /* BIRDII accent color */

            /* Birdii Brand Colors */
            --color-primary: #555555; /* Light dark as primary color (was teal) */
            --color-secondary: #4BD28F; /* BIRDII secondary color */
            --color-light-dark: #555555; /* Light dark shade */

            /* Spacing */
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;

            /* Border radius - NYT uses minimal rounding */
            --border-radius-sm: 2px;
            --border-radius-md: 3px;
        }

        body {
            font-family: var(--font-imperial);
            color: var(--color-dark-gray);
            line-height: 1.6;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            background-attachment: fixed;
            padding-top: var(--spacing-xl);
            padding-bottom: var(--spacing-xl);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* Logo and header styling */
        .birdii-logo {
            font-size: 1.5rem;
            font-weight: 700;
            letter-spacing: -0.02em;
            color: #00BFFF;
            font-family: sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .birdii-logo i {
            display: none;
        }

        .birdii-logo svg, .birdii-logo img {
            width: 160px;
            height: 107px;
            max-width: 100%;
            height: auto;
        }

        /* Responsive logo scaling */
        @media (max-width: 768px) {
            .birdii-logo svg, .birdii-logo img {
                width: 140px;
                height: 93px;
            }
        }

        @media (max-width: 576px) {
            .birdii-logo svg, .birdii-logo img {
                width: 120px;
                height: 80px;
            }
        }

        @media (max-width: 480px) {
            .birdii-logo svg, .birdii-logo img {
                width: 100px;
                height: 67px;
            }
        }

        /* Responsive header styling */
        @media (max-width: 768px) {
            .responsive-header {
                padding: 1rem !important;
                margin-bottom: 2rem !important;
            }

            .responsive-subtitle {
                font-size: 0.95rem !important;
            }
        }

        @media (max-width: 576px) {
            .responsive-header {
                padding: 0.75rem !important;
                margin-bottom: 1.5rem !important;
            }

            .responsive-subtitle {
                font-size: 0.9rem !important;
                padding: 0 0.5rem;
            }
        }

        @media (max-width: 480px) {
            .responsive-header {
                padding: 0.5rem !important;
                margin-bottom: 1rem !important;
            }

            .responsive-subtitle {
                font-size: 0.85rem !important;
                padding: 0 0.25rem;
            }
        }

        .site-title {
            font-family: sans-serif;
            color: var(--color-primary); /* Updated to teal primary color */
            font-weight: 700;
            font-size: 2rem;
            margin-bottom: 2px;
            display: block;
            letter-spacing: -0.02em;
            /* Replacing gradient with solid color for NYT style */
            background: none;
            -webkit-text-fill-color: inherit;
        }

        .site-subtitle {
            font-family: var(--font-imperial);
            font-size: 1rem;
            color: var(--color-medium-gray);
            font-weight: 400;
            display: block;
            margin-top: -12px;
            margin-bottom: 10px;
            letter-spacing: 0.01em;
            line-height: 1.3;
        }

        .topic-btn {
            font-family: var(--font-franklin);
            letter-spacing: 0.02em;
            font-size: 0.9rem;
            border-radius: var(--border-radius-sm);
            border: 1px solid var(--color-border);
            transition: all 0.2s ease;
        }

        .topic-btn.active {
            background-color: var(--color-light-gray);
            color: var(--color-black);
            border-color: var(--color-black);
            font-weight: 500;
        }

        /* Summary formatting styles */
        .summary-structure {
            background-color: white;
            border-radius: var(--border-radius-md);
            padding: 25px 30px;
            margin-bottom: var(--spacing-lg);
            border-top: 1px solid var(--color-border);
            border-left: none; /* Removing the left border for NYT style */
            box-shadow: 0 1px 2px rgba(0,0,0,0.03);
        }

        .summary-structure .article-summary {
            font-family: var(--font-imperial);
            color: var(--color-dark-gray);
            font-size: 1.05rem;
            margin-bottom: var(--spacing-lg);
            line-height: 1.6;
            border-bottom: 1px solid var(--color-border);
            padding-bottom: var(--spacing-lg);
            text-align: left; /* NYT typically uses left alignment */
            max-height: none;
            overflow-y: visible;
            position: relative;
            letter-spacing: 0.01em;
        }

        .summary-structure .main-theme {
            font-family: var(--font-cheltenham);
            color: var(--color-black);
            font-size: 1.2rem;
            margin-bottom: var(--spacing-md);
            font-weight: 700;
            letter-spacing: -0.01em;
        }

        /* Enhanced professional key points - journalistic style */
        .summary-structure .key-points {
            margin-left: 0;
            background-color: white;
            border-radius: var(--border-radius-sm);
            padding: var(--spacing-lg);
            box-shadow: none;
            border: 1px solid var(--color-border);
        }

        .summary-structure .key-points p {
            font-family: var(--font-franklin);
            margin-bottom: var(--spacing-md);
            font-weight: 700;
            color: var(--color-black);
            border-bottom: 1px solid var(--color-border);
            padding-bottom: var(--spacing-sm);
            font-size: 0.95rem;
            letter-spacing: 0.02em;
            text-transform: uppercase;
        }

        .summary-structure .key-points ul {
            padding-left: 22px;
            margin-bottom: 0;
        }

        .summary-structure .key-points li {
            font-family: var(--font-imperial);
            margin-bottom: var(--spacing-md);
            color: var(--color-dark-gray);
            font-size: 1rem;
            line-height: 1.5;
            position: relative;
            padding-left: var(--spacing-xs);
            letter-spacing: 0.01em;
        }

        .summary-structure .key-points li:last-child {
            margin-bottom: 0;
        }

        .summary-structure .key-points li::marker {
            color: var(--color-black);
            font-weight: bold;
        }

        .summary-structure .takeaway {
            font-family: var(--font-imperial);
            font-style: italic;
            color: var(--color-dark-gray);
            font-size: 1rem;
            border-top: 1px solid var(--color-border);
            padding-top: var(--spacing-sm);
            margin-top: var(--spacing-xs);
        }

        /* Background container styles */
        .background-container {
            transition: all 0.3s ease;
        }

        /* Card styling improvements */
        .card {
            border-radius: var(--border-radius-sm);
            border: 1px solid var(--color-border);
            box-shadow: none;
            transition: all 0.2s ease;
        }

        .card:hover {
            box-shadow: 0 2px 4px rgba(0,0,0,0.08);
        }

        .card-title {
            font-family: var(--font-cheltenham);
            font-weight: 700;
            letter-spacing: -0.01em;
            line-height: 1.3;
        }

        .card-subtitle {
            font-family: var(--font-franklin);
            font-size: 0.85rem;
            color: var(--color-medium-gray);
            letter-spacing: 0.01em;
        }

        /* Styling for welcome message and sources container */
        .welcome-message {
            padding: var(--spacing-lg) var(--spacing-md);
            font-family: var(--font-imperial);
        }

        .sources-container {
            max-width: 800px;
            margin: 0 auto;
        }

        /* Original source badge style for the initial badges */
        .source-badge {
            font-family: var(--font-franklin);
            padding: 6px 12px;
            font-weight: 400;
            font-size: 0.85rem;
            border: 1px solid var(--color-border);
            border-radius: var(--border-radius-sm);
            box-shadow: none;
            background-color: white;
        }

        /* Gradient style for news sources */
        .news-source-badge {
            font-family: var(--font-franklin);
            padding: 6px 12px;
            font-weight: 400;
            font-size: 0.85rem;
            border: none;
            border-radius: var(--border-radius-sm);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            background: linear-gradient(90deg, #0A95FB 0%, #4BD28F 100%);
            color: white;
            text-shadow: 0 1px 1px rgba(0,0,0,0.2);
            display: inline-flex;
            align-items: center;
            height: 28px; /* Fixed height to ensure consistency */
        }

        .nav-link {
            font-family: var(--font-franklin);
            font-size: 0.95rem;
            font-weight: 500;
            color: var(--color-black);
            text-transform: uppercase;
            letter-spacing: 0.02em;
            margin-left: var(--spacing-lg);
            padding: var(--spacing-xs) var(--spacing-md);
            border-radius: var(--border-radius-sm);
            transition: all 0.2s ease;
        }

        .nav-link:hover {
            background-color: var(--color-light-gray);
            color: var(--color-link);
        }

        .nav-link i {
            margin-right: var(--spacing-xs);
        }

        /* Links styling */
        a {
            color: var(--color-link);
            text-decoration: none;
            transition: color 0.2s ease;
        }

        a:hover {
            color: var(--color-link-hover);
            text-decoration: underline;
        }

        /* NYT style blockquote */
        blockquote {
            font-family: var(--font-imperial);
            font-style: italic;
            border-left: 3px solid var(--color-border);
            padding-left: var(--spacing-md);
            margin-left: var(--spacing-md);
            color: var(--color-medium-gray);
        }

        /* Footer positioning */
        .container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 1200px;
            padding: 0 var(--spacing-lg);
        }

        main {
            flex: 1;
        }

        footer {
            margin-top: auto;
            padding-bottom: 1.5rem;
            font-family: var(--font-franklin);
            font-size: 0.85rem;
            color: var(--color-medium-gray);
        }

        /* Action button styling - added for consistency */
        .btn-primary {
            background-color: var(--color-primary);
            border-color: var(--color-primary);
            color: white;
        }

        .btn-primary:hover {
            background-color: #444444; /* Darker light dark */
            border-color: #444444;
        }

        .btn-secondary {
            background: linear-gradient(90deg, #0A95FB 0%, #4BD28F 100%);
            border: none;
            color: white;
            text-shadow: 0 1px 1px rgba(0,0,0,0.2);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .btn-secondary:hover {
            background: linear-gradient(90deg, #0986e0 0%, #42bd7f 100%);
            color: white;
            box-shadow: 0 3px 5px rgba(0,0,0,0.15);
        }

        /* Add some styling for the enriched context */
        .enriched-context {
            font-family: var(--font-imperial);
            font-size: 1rem;
            line-height: 1.6;
            color: var(--color-dark-gray);
        }

        /* Gradient button styling to match the badge styling */
        .gradient-button {
            background: linear-gradient(90deg, #0A95FB 0%, #4BD28F 100%);
            border: none;
            color: white;
            text-shadow: 0 1px 1px rgba(0,0,0,0.2);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .gradient-button:hover {
            background: linear-gradient(90deg, #0986e0 0%, #42bd7f 100%);
            color: white;
            box-shadow: 0 3px 5px rgba(0,0,0,0.15);
        }

        .gradient-button:active, .gradient-button:focus {
            color: white;
            box-shadow: 0 2px 3px rgba(0,0,0,0.1);
        }

        .enriched-context h5 {
            font-family: var(--font-franklin);
            color: var(--color-black);
            border-bottom: 1px solid var(--color-border);
            padding-bottom: var(--spacing-sm);
            margin-bottom: var(--spacing-lg);
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.03em;
            font-size: 0.9rem;
        }

        .enriched-context h6 {
            font-family: var(--font-cheltenham);
            color: var(--color-black);
            margin-top: var(--spacing-xl);
            margin-bottom: var(--spacing-md);
            font-weight: 700;
            font-size: 1.15rem;
            padding-left: 0;
            letter-spacing: -0.01em;
        }

        .enriched-context p {
            margin-bottom: var(--spacing-lg);
            text-align: left;
            font-size: 1.05rem;
            line-height: 1.6;
        }

        .enriched-context ul {
            padding-left: 20px;
            margin-bottom: var(--spacing-lg);
        }

        .enriched-context ul li {
            margin-bottom: var(--spacing-sm);
            line-height: 1.5;
        }

        .enriched-context ul {
            padding-left: 20px;
            margin-bottom: var(--spacing-lg);
            list-style-type: disc;
        }

        .enriched-context ul li {
            margin-bottom: var(--spacing-sm);
            line-height: 1.5;
            font-family: var(--font-imperial);
            color: var(--color-dark-gray);
        }

        .enriched-context ul li::marker {
            color: var(--color-black);
        }

        /* Nested lists styling */
        .enriched-context ul ul {
            margin-top: var(--spacing-xs);
            margin-bottom: var(--spacing-xs);
            padding-left: 15px;
        }

        .enriched-context ul ul li {
            margin-bottom: var(--spacing-xs);
            font-size: 0.95rem;
        }

        .enriched-context .alert {
            margin-top: var(--spacing-md);
            border-radius: var(--border-radius-sm);
            font-family: var(--font-franklin);
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="pb-4 mb-5 responsive-header" style="background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.9) 100%); border-radius: 1rem; padding: 1.5rem; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); border: 1px solid rgba(229, 231, 235, 0.5);">
            <a href="/" class="text-decoration-none">
                <div class="d-flex flex-column align-items-center text-center">
                    <div class="birdii-logo mb-2">
                        <img src="{{ url_for('static', filename='img/birdii_logo.svg') }}" alt="BIRDII Logo" width="160" height="107">
                    </div>
                    <span class="site-subtitle responsive-subtitle" style="font-size: 1.1rem; color: var(--color-medium-gray); font-weight: 400;">News with context, knowledge with every read</span>
                </div>
            </a>
        </header>

        <main>
            {% block content %}{% endblock %}
        </main>

        <footer class="pt-5 mt-5 text-muted border-top">
            <div class="text-center my-4">
                © 2025 birdii. All Rights Reserved.    <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">Terms of Use</a>
            </div>
        </footer>
    </div>

    <!-- Terms of Use Modal -->
    <div class="modal fade" id="termsModal" tabindex="-1" aria-labelledby="termsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="termsModalLabel">Terms of Use</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <h5>1. Introduction</h5>
                    <p>These Terms of Use ("Terms") govern your use of BIRDII ("the App"), a web application provided by BIRDII ("we," "us," or "our"). By accessing or using the App, you agree to be bound by these Terms. The App is designed to deliver news aggregation, summarization, AI-generated context, and interactive question-answering features to enhance your news experience.</p>

                    <h5>2. Acceptance of Terms</h5>
                    <p>By using the App, you confirm that you have read, understood, and agree to these Terms. If you do not accept these Terms, you must refrain from using the App.</p>

                    <h5>3. User Conduct</h5>
                    <p>You agree to use the App responsibly and not for any illegal or harmful purposes. The following activities are prohibited:</p>
                    <ul>
                        <li>Posting or sharing content that is unlawful, harmful, or offensive.</li>
                        <li>Attempting to access the App or its systems without authorization.</li>
                        <li>Disrupting or impairing the App's functionality.</li>
                        <li>Using the App to send spam or unsolicited messages.</li>
                        <li>Violating the intellectual property rights of others.</li>
                    </ul>

                    <h5>4. Intellectual Property</h5>
                    <p>All content on the App—including text, graphics, logos, software, and AI-generated material—is owned by BIRDII or its licensors and is protected by copyright, trademark, and other intellectual property laws. You may not copy, modify, distribute, or create derivative works from the App's content without our prior written permission.</p>
                    <p>Content produced by the App's AI features, such as contextual insights and responses to user questions, belongs to BIRDII. Users receive a limited, non-exclusive license to use this content for personal, non-commercial purposes only.</p>


                    <h5>5. Disclaimer of Warranties</h5>
                    <p>The App is provided "as is" and "as available," without any warranties, express or implied. We do not guarantee that the App will be uninterrupted, error-free, or free from viruses or other harmful elements. Additionally, we do not warrant the accuracy, completeness, or reliability of the news content, summaries, or AI-generated context offered through the App.</p>

                    <h5>6. Limitation of Liability</h5>
                    <p>To the maximum extent allowed by law, BIRDII is not liable for any indirect, incidental, special, or consequential damages resulting from your use of the App. This includes, but is not limited to, damages for loss of profits, data, or other intangible losses.</p>

                    <h5>7. Indemnification</h5>
                    <p>You agree to indemnify, defend, and hold harmless BIRDII, its officers, directors, employees, and agents from any claims, liabilities, damages, losses, or expenses arising from your use of the App or any violation of these Terms.</p>

                    <h5>8. Governing Law</h5>
                    <p>These Terms are governed by and interpreted under the laws of [Your Jurisdiction], without regard to its conflict of law principles.</p>

                    <h5>9. Changes to Terms</h5>
                    <p>We may update these Terms at any time, and changes will take effect immediately upon posting. Your continued use of the App after such updates signifies your acceptance of the revised Terms.</p>

                    <h5>10. Dispute Resolution</h5>
                    <p>Any disputes related to these Terms or the App will be resolved through binding arbitration under the rules of [Arbitration Organization]. The arbitrator's decision may be enforced in any court with appropriate jurisdiction.</p>
                </div>
                <div class="modal-footer">
                    <!-- Close button removed -->
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>