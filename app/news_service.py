from newsdataapi import NewsDataApiClient
from datetime import datetime, timedelta
import os
import random
from newspaper import Article
import threading
import time
import json
import hashlib
from dotenv import load_dotenv
from flask import has_app_context, current_app
import feedparser
import re
from bs4 import BeautifulSoup
import requests
from urllib.parse import urljoin, urlparse

# Load environment variables
load_dotenv()

class NewsService:
    def __init__(self):
        # NewsData.io client initialization
        api_key = os.environ.get('NEWS_DATA_API_KEY')
        if not api_key:
            raise ValueError("NEWS_DATA_API_KEY environment variable is not set. Please add it to your .env file.")

        self.newsdata_client = NewsDataApiClient(apikey=api_key)

        # RSS feeds configuration for newsletters and other sources
        self.rss_feeds = {
            'technology': [
                {
                    'url': 'https://newsletter.pragmaticengineer.com/feed',
                    'name': 'The Pragmatic Engineer',
                    'description': 'Engineering leadership and industry insights'
                }
            ],
            'business': [
                {
                    'url': 'https://blog.hubspot.com/marketing/rss.xml',
                    'name': 'HubSpot Blog',
                    'description': 'Business growth and marketing insights'
                },
                {
                    'url': 'https://blog.bufferapp.com/feed/',
                    'name': 'Buffer Blog',
                    'description': 'Social media and business strategy'
                }
            ],
            'science': [
                {
                    'url': 'https://www.quantamagazine.org/feed/',
                    'name': 'Quanta Magazine',
                    'description': 'Science and mathematics blog'
                },
                {
                    'url': 'https://blog.ted.com/feed/',
                    'name': 'TED Blog',
                    'description': 'Ideas worth spreading'
                }
            ],
            'design': [
                {
                    'url': 'https://www.smashingmagazine.com/feed/',
                    'name': 'Smashing Magazine',
                    'description': 'Web design and development blog'
                },
                {
                    'url': 'https://www.awwwards.com/blog/feed/',
                    'name': 'Awwwards Blog',
                    'description': 'Web design inspiration and trends'
                }
            ],
            'marketing': [
                {
                    'url': 'https://blog.hubspot.com/marketing/rss.xml',
                    'name': 'HubSpot Marketing Blog',
                    'description': 'Marketing strategies and insights'
                }
            ],
            'ai & machine learning': [
                {
                    'url': 'https://distill.pub/rss.xml',
                    'name': 'Distill',
                    'description': 'Machine learning research blog'
                }
            ],
            'startups': [
                {
                    'url': 'https://blog.ycombinator.com/feed/',
                    'name': 'Y Combinator Blog',
                    'description': 'Startup advice and insights'
                }
            ],
            'finance': [
                {
                    'url': 'https://blog.wealthfront.com/feed/',
                    'name': 'Wealthfront Blog',
                    'description': 'Investment and wealth management insights'
                }
            ]
        }

        # List of high-quality news sources for diversity
        # Includes all possible formats that NewsData.io might use for these sources
        self.premium_sources = [
            # US sources - domains
            'nytimes.com', 'washingtonpost.com', 'wsj.com',
            'cnn.com', 'foxnews.com', 'usatoday.com',
            'nbcnews.com', 'cbsnews.com', 'abcnews.go.com',
            'bloomberg.com', 'politico.com', 'reuters.com', 'apnews.com', 'ap.org',

            # UK sources - domains
            'bbc.com', 'bbc.co.uk', 'theguardian.com', 'guardian.co.uk', 'telegraph.co.uk',
            'independent.co.uk', 'reuters.com', 'apnews.com',

            # US sources - source_id style (NewsData.io format variations)
            'cnn', 'nytimes', 'ny times', 'new york times', 'nyt', 'the new york times',
            'washingtonpost', 'washington post', 'the washington post', 'wapo',
            'wsj', 'wall street journal', 'the wall street journal',
            'ap', 'ap news', 'associated press', 'apnews',
            'reuters', 'bloomberg', 'bloomberg news',
            'usatoday', 'usa today', 'fox news', 'foxnews',
            'nbcnews', 'nbc news', 'nbc', 'cbsnews', 'cbs news', 'cbs',
            'abcnews', 'abc news', 'abc',
            'politico', 'npr', 'national public radio',

            # UK sources - source_id style (NewsData.io format variations)
            'bbc', 'bbc news', 'the bbc', 'bbc world', 'breaking news',
            'theguardian', 'guardian', 'the guardian',
            'telegraph', 'the telegraph', 'the daily telegraph',
            'independent', 'the independent',
            'ft', 'financial times', 'the financial times',
            'economist', 'the economist', 'times',

            # Source name variations (with capitalization)
            'The New York Times', 'New York Times', 'NY Times', 'NYT',
            'The Washington Post', 'Washington Post', 'WaPo',
            'The Wall Street Journal', 'Wall Street Journal', 'WSJ',
            'CNN', 'Fox News', 'USA Today', 'CNBC',
            'NBC News', 'CBS News', 'ABC News',
            'Bloomberg', 'Politico', 'Reuters', 'Associated Press', 'AP',
            'BBC', 'BBC News', 'The Guardian', 'Guardian',
            'The Telegraph', 'Telegraph', 'The Independent', 'Independent',
            'Financial Times', 'FT', 'The Economist', 'Economist',
        ]

        # Top-tier trusted sources (most reputable)
        # These sources will be prioritized above other premium sources
        self.top_tier_sources = [
            # US top-tier sources - all variations
            'cnn', 'cnn.com', 'CNN',
            'nytimes', 'ny times', 'nyt', 'new york times', 'the new york times', 'nytimes.com', 'New York Times', 'The New York Times', 'NYT',
            'reuters', 'reuters.com', 'Reuters',
            'ap', 'ap news', 'associated press', 'apnews', 'apnews.com', 'ap.org', 'Associated Press', 'AP',
            'wsj', 'wall street journal', 'the wall street journal', 'wsj.com', 'Wall Street Journal', 'The Wall Street Journal', 'WSJ',
            'washingtonpost', 'washington post', 'the washington post', 'washingtonpost.com', 'Washington Post', 'The Washington Post',

            # UK top-tier sources - all variations
            'bbc', 'bbc news', 'the bbc', 'bbc world', 'bbc.com', 'bbc.co.uk', 'BBC', 'BBC News',
            'theguardian', 'guardian', 'the guardian', 'theguardian.com', 'guardian.co.uk', 'Guardian', 'The Guardian',
            'ft', 'financial times', 'the financial times', 'apnews.com', 'Financial Times', 'FT',
            'economist', 'the economist', 'reuters.com', 'Economist', 'The Economist'
        ]

        # List of domains confirmed to work with NewsData.io API
        self.verified_domains = [
            # USA sources (full list including common variations)
            'cnn', 'nytimes', 'ny-times', 'nyt', 'new-york-times', 'washingtonpost',
            'washington-post', 'wsj', 'wall-street-journal', 'foxnews', 'fox-news',
            'usatoday', 'usa-today', 'reuters', 'bloomberg', 'ap', 'apnews', 'associated-press',
            'nbcnews', 'nbc-news', 'cbsnews', 'cbs-news', 'abcnews', 'abc-news',
            'politico', 'npr', 'cnbc',

            # UK sources (full list including common variations)
            'bbc', 'bbc-news', 'theguardian', 'guardian', 'telegraph', 'independent',
            'ft', 'financial-times', 'economist', 'times', 'daily-mail', 'mirror',
            'the-sun', 'standard', 'metro', 'daily-express', 'sky-news',
        ]

        # News cache for storing pre-fetched news
        self.news_cache = {}
        self.news_cache_last_updated = {}
        self.cache_lock = threading.Lock()
        self._cache_loaded = False

        # Request deduplication tracking
        self.active_requests = {}  # Track ongoing API requests by topic
        self.request_lock = threading.Lock()

        # API quota tracking
        self.api_calls_today = 0
        self.api_calls_reset_time = time.time() + 86400  # Next 24 hours
        self.api_quota_lock = threading.Lock()
        self.daily_limit = 1000  # NewsData.io paid plan limit (previously 200 for free tier)
        self.is_rate_limited = False

        # Cache validity periods (in seconds)
        self.cache_fresh_period = 7200   # 2 hours - considered fresh
        self.cache_stale_period = 43200  # 12 hours - still usable but refresh in background
        self.cache_max_period = 86400    # 24 hours - absolute maximum before forced refresh

        # Popular topics to pre-fetch
        self.popular_topics = [
            'sports', 'technology', 'business', 'science', 'entertainment',
            'health', 'world', 'politics'
        ]

        # Popular RSS topics to pre-fetch (subset of available RSS feeds)
        self.popular_rss_topics = [
            'technology', 'business', 'science'
        ]

        print("NewsService initialized - background thread will start with first request")

    def start_background_refresh(self):
        """Start a background thread to periodically refresh news for popular topics"""
        print("Starting background news refresh thread")
        refresh_thread = threading.Thread(target=self._background_refresh_worker, daemon=True)
        refresh_thread.start()

    def _background_refresh_worker(self):
        """Background worker that refreshes news for popular topics"""
        while True:
            try:
                print("Starting background news refresh...")
                
                # Refresh NewsData API topics
                for topic in self.popular_topics:
                    try:
                        # Check if we're rate limited before proceeding
                        with self.api_quota_lock:
                            if self.is_rate_limited:
                                print(f"Skipping refresh for {topic} due to API rate limiting")
                                continue

                        # Add safety check for None topic
                        if topic is None or topic == '':
                            print(f"Skipping refresh for empty topic")
                            continue

                        print(f"Refreshing news for topic: {topic}")
                        self._refresh_topic(topic)
                        # Sleep between topics to avoid API rate limiting
                        time.sleep(5)
                    except Exception as e:
                        print(f"Error refreshing topic {topic}: {str(e)}")

                # Refresh RSS topics (no API rate limiting concerns)
                print("Starting RSS background refresh...")
                for rss_topic in self.popular_rss_topics:
                    try:
                        if rss_topic is None or rss_topic == '':
                            print(f"Skipping refresh for empty RSS topic")
                            continue

                        print(f"Refreshing RSS for topic: {rss_topic}")
                        self._refresh_rss_topic(rss_topic)
                        # Short sleep between RSS refreshes
                        time.sleep(2)
                    except Exception as e:
                        print(f"Error refreshing RSS topic {rss_topic}: {str(e)}")

                # Log cache stats after refresh
                with self.cache_lock:
                    news_cache_count = len([k for k in self.news_cache.keys() if k.startswith('news_')])
                    rss_cache_count = len([k for k in self.news_cache.keys() if k.startswith('rss_')])
                    print(f"Cache status: {news_cache_count} news topics, {rss_cache_count} RSS topics cached")
                    print(f"API calls today: {self.api_calls_today}/{self.daily_limit}")

                # Sleep for 2 hours before next refresh cycle
                time.sleep(7200)
            except Exception as e:
                print(f"Error in background refresh thread: {str(e)}")
                # Sleep for 5 minutes on error before retrying
                time.sleep(300)

    def _increment_api_counter(self):
        """Track API usage to avoid hitting rate limits"""
        with self.api_quota_lock:
            # Reset counter if a new day has started
            current_time = time.time()
            if current_time > self.api_calls_reset_time:
                self.api_calls_today = 0
                self.api_calls_reset_time = current_time + 86400  # Reset time to 24 hours from now
                self.is_rate_limited = False
                print("API call counter reset for new day")

            # Increment the counter
            self.api_calls_today += 1

            # Check if we're approaching the limit
            if self.api_calls_today >= self.daily_limit:
                self.is_rate_limited = True
                print(f"WARNING: API daily limit reached ({self.api_calls_today}/{self.daily_limit})")
            elif self.api_calls_today >= self.daily_limit * 0.9:  # 90% of limit
                print(f"CAUTION: API usage at {self.api_calls_today}/{self.daily_limit} (90% of daily limit)")

            return self.api_calls_today

    def _load_cache_from_db(self):
        """Load cached news from the database"""
        if self._cache_loaded:
            print("Cache already loaded from DB, skipping")
            return

        def load_cache():
            try:
                from app.models import NewsCache
                from app import db

                # Clean up old cache entries (older than 24 hours)
                cleanup_time = datetime.utcnow() - timedelta(hours=24)
                db.session.query(NewsCache).filter(NewsCache.timestamp < cleanup_time).delete()
                db.session.commit()

                # Load cache entries
                cache_entries = NewsCache.query.all()
                print(f"Loading {len(cache_entries)} cache entries from database")

                with self.cache_lock:
                    for entry in cache_entries:
                        try:
                            cache_data = json.loads(entry.data)
                            self.news_cache[entry.topic_key] = cache_data
                            self.news_cache_last_updated[entry.topic_key] = entry.timestamp.timestamp()
                        except Exception as e:
                            print(f"Error loading cache entry: {str(e)}")

                print(f"Successfully loaded {len(cache_entries)} cache entries")
                self._cache_loaded = True

            except Exception as e:
                print(f"Error loading cache from DB: {str(e)}")
                return False

            return True

        # Check if we're in an application context
        if has_app_context():
            return load_cache()
        else:
            # We need an app context to access the database
            print("No application context available, will load cache later")
            return False

    def _refresh_topic(self, topic):
        """Refresh news for a specific topic"""
        try:
            # Create cache key
            cache_key = self._create_cache_key(topic)
            
            # Check if there's already an active request for this topic
            with self.request_lock:
                if cache_key in self.active_requests:
                    print(f"Refresh request already in progress for topic: {topic}, skipping duplicate")
                    return False
                
                # Mark this request as active
                request_event = threading.Event()
                self.active_requests[cache_key] = request_event

            try:
                # Fetch fresh news
                api_response = self._fetch_from_api(topic)

                if api_response and 'results' in api_response and api_response['results']:
                    # Update cache with thread safety
                    with self.cache_lock:
                        self.news_cache[cache_key] = api_response
                        self.news_cache_last_updated[cache_key] = time.time()

                    print(f"Successfully refreshed news for topic: {topic}")
                    return True
                else:
                    print(f"No articles found for topic: {topic}")
                    return False
            
            finally:
                # Always clean up the active request tracking
                with self.request_lock:
                    if cache_key in self.active_requests:
                        self.active_requests[cache_key].set()  # Signal completion
                        del self.active_requests[cache_key]
                        
        except Exception as e:
            print(f"Error refreshing news for topic {topic}: {str(e)}")
            
            # Clean up active request tracking on error
            try:
                with self.request_lock:
                    if cache_key in self.active_requests:
                        self.active_requests[cache_key].set()
                        del self.active_requests[cache_key]
            except:
                pass
                
            return False

    def _create_cache_key(self, topic):
        """Create a standardized cache key for a topic"""
        if topic is None or topic == '':
            topic = 'general'
        return f"news_{topic.lower().replace(' & ', '_').replace(' ', '_')}"

    def _fetch_from_api(self, topic, cache_params=None):
        """Fetch articles from the NewsData.io API for a given topic"""
        print(f"Fetching articles from NewsData API for topic: {topic}")

        try:
            # Map frontend topic to the right API parameters
            api_params = self.map_topic_to_query(topic)

            # Set up base parameters
            base_params = {
                'language': 'en',
                'size': 5  # Reduced to 5 articles per domain for better diversity
            }

            # Check if we're rate limited
            with self.api_quota_lock:
                if self.is_rate_limited:
                    print(f"API rate limited - skipping fetch for topic: {topic}")
                    return {"results": []}

            # If this is a topic with specific domains, query each domain separately
            topic_lower = topic.lower() if topic else 'general'
            all_results = []

            if topic_lower in self._get_topic_domains():
                domain_list = self._get_topic_domains()[topic_lower]
                print(f"Making multiple API calls for topic '{topic}' - one per domain, requesting 5 articles from each")

                # Make a separate API call for each domain
                for domain in domain_list:
                    try:
                        # Use relaxed constraints approach for all sources
                        # This gets more articles and allows us to select the 5 most recent
                        domain_params = {
                            'domainurl': domain,
                            'language': 'en',
                            'size': 15  # Get more articles to have better selection
                        }
                        
                        print(f"Making API request to NewsData for domain: {domain} (relaxed constraints)")

                        # Check if we're rate limited before proceeding
                        with self.api_quota_lock:
                            if self.is_rate_limited:
                                print(f"API rate limited - skipping domain: {domain}")
                                continue

                        # Increment API call counter
                        self._increment_api_counter()

                        # Make the API call
                        response = self.newsdata_client.latest_api(**domain_params)

                        # Robust response validation to handle unexpected formats
                        if (isinstance(response, dict) and
                            response.get('status') == 'success' and
                            isinstance(response.get('results'), list) and
                            len(response.get('results', [])) > 0):

                            articles = response.get('results', [])
                            
                            # Sort articles by publication date (most recent first) and take top 5
                            sorted_articles = sorted(articles, 
                                                   key=lambda x: x.get('pubDate', ''), 
                                                   reverse=True)
                            top_5_articles = sorted_articles[:5]
                            
                            print(f"API call successful for domain {domain}: got {len(articles)} articles, selected {len(top_5_articles)} most recent")
                            if top_5_articles:
                                latest_date = top_5_articles[0].get('pubDate', 'No date')
                                oldest_date = top_5_articles[-1].get('pubDate', 'No date')
                                print(f"  Date range: {oldest_date} to {latest_date}")
                            
                            all_results.extend(top_5_articles)
                        else:
                            # Extract error message if possible, with fallbacks for unexpected response formats
                            error_msg = "Unknown error"
                            if isinstance(response, dict):
                                if isinstance(response.get('results'), dict):
                                    error_msg = response.get('results', {}).get('message', 'Unknown error')
                                elif isinstance(response.get('message'), (str, list, dict)):
                                    error_msg = response.get('message')

                            print(f"API call failed for domain {domain}: {error_msg}")
                            print(f"Response type: {type(response).__name__}, Content: {str(response)[:200]}")  # Log response for debugging
                    except Exception as e:
                        print(f"Error with API call for domain {domain}: {str(e)}")

                    # Add a small delay between domain requests to avoid overloading the API
                    time.sleep(1)

                # If we have results from any domain, create a combined response
                if all_results:
                    combined_response = {
                        "status": "success",
                        "results": all_results
                    }
                    try:
                        # Ensure we have a diverse set of sources and no duplicates
                        self.ensure_source_diversity(combined_response)
                    except Exception as e:
                        print(f"Error ensuring source diversity: {str(e)}")

                    return combined_response

                # If we didn't get any results from specific domains, fall back to a generic query
                print(f"No results from domain-specific queries, trying generic query for {topic}")

            # Make a generic query without domain restriction (as fallback or for topics without domains)
            generic_params = api_params.copy()
            generic_params.update(base_params)

            # Remove domainurl if it exists
            if "domainurl" in generic_params:
                del generic_params["domainurl"]

            generic_params["size"] = 30  # Increase size for generic query

            # Log what we're about to request
            print(f"Making generic API request to NewsData with params: {generic_params}")

            # Increment API call counter
            self._increment_api_counter()

            try:
                # Make the API call
                print(f"Making fallback NewsData API call for topic '{topic}'")
                response = self.newsdata_client.latest_api(**generic_params)

                # Use the same robust response validation for generic query
                if (isinstance(response, dict) and
                    response.get('status') == 'success' and
                    isinstance(response.get('results'), list) and
                    len(response.get('results', [])) > 0):

                    # If we already have results from domain-specific queries, combine them
                    if all_results:
                        print(f"Combining {len(all_results)} domain-specific results with {len(response.get('results', []))} generic results")
                        all_results.extend(response.get('results', []))
                        combined_response = {
                            "status": "success",
                            "results": all_results
                        }
                        try:
                            self.ensure_source_diversity(combined_response)
                        except Exception as e:
                            print(f"Error ensuring source diversity: {str(e)}")
                        return combined_response
                    else:
                        # Otherwise return just the generic results
                        print(f"API call successful: got {len(response.get('results', []))} articles")
                        try:
                            self.ensure_source_diversity(response)
                        except Exception as e:
                            print(f"Error ensuring source diversity: {str(e)}")
                        return response
                else:
                    # Extract error message with the same robust approach
                    error_msg = "Unknown error"
                    if isinstance(response, dict):
                        if isinstance(response.get('results'), dict):
                            error_msg = response.get('results', {}).get('message', 'Unknown error')
                        elif isinstance(response.get('message'), (str, list, dict)):
                            error_msg = response.get('message')

                    print(f"Generic API call failed: {error_msg}")
                    print(f"Response type: {type(response).__name__}, Content: {str(response)[:200]}")  # Log response for debugging

                    # If we have domain-specific results but generic query failed, still return domain results
                    if all_results:
                        combined_response = {
                            "status": "success",
                            "results": all_results
                        }
                        return combined_response

                    # If everything failed, return empty response
                    return {"results": []}
            except Exception as e:
                # If generic query failed but we have domain results, still return them
                if all_results:
                    combined_response = {
                        "status": "success",
                        "results": all_results
                    }
                    return combined_response

                print(f"Error with API call: {str(e)}. Returning empty results.")
                return {"results": []}
        except Exception as e:
            print(f"Error in _fetch_from_api: {str(e)}")
            return {"results": []}

    def _fetch_from_rss(self, topic):
        """
        Fetch articles from RSS feeds for a given topic with caching.
        Uses tiered caching similar to NewsData API.
        
        Args:
            topic (str): Topic to fetch RSS feeds for
            
        Returns:
            dict: Formatted response similar to NewsData API
        """
        try:
            # Create cache key for RSS feeds
            cache_key = f"rss_{topic.lower().replace(' & ', '_').replace(' ', '_')}"

            # Try to get from cache first
            with self.cache_lock:
                cached_data = self.news_cache.get(cache_key)
                last_updated = self.news_cache_last_updated.get(cache_key, 0)

            # Check if cache is valid
            current_time = time.time()
            cache_age = current_time - last_updated

            # RSS cache validity periods (RSS feeds update less frequently than news)
            rss_fresh_period = 1800    # 30 minutes - considered fresh
            rss_stale_period = 7200    # 2 hours - still usable but refresh in background
            rss_max_period = 21600     # 6 hours - absolute maximum before forced refresh

            # Fast path: Fresh cache (under 30 minutes)
            if cached_data and cache_age < rss_fresh_period:
                print(f"Fresh RSS cache hit for topic: {topic} (age: {cache_age/60:.1f} min)")
                return cached_data

            # Tier 2: Stale but usable cache (30min-2hr) - use cache but refresh in background
            if cached_data and cache_age < rss_stale_period:
                print(f"Stale RSS cache hit for topic: {topic} (age: {cache_age/3600:.1f} hours), refreshing in background")
                
                # Start background refresh without waiting for result
                threading.Thread(target=self._refresh_rss_topic, args=(topic,), daemon=True).start()
                
                # Return cached data immediately
                return cached_data

            # Tier 3: Expired cache (2hr-6hr) - try to fetch fresh, fallback to cache
            if cached_data and cache_age < rss_max_period:
                print(f"Expired RSS cache for topic: {topic} (age: {cache_age/3600:.1f} hours), trying fresh fetch with fallback")
                
                # Try to fetch fresh data
                fresh_data = self._fetch_from_rss_uncached(topic)
                
                if fresh_data and fresh_data.get('results'):
                    # Update cache with fresh data
                    with self.cache_lock:
                        self.news_cache[cache_key] = fresh_data
                        self.news_cache_last_updated[cache_key] = time.time()
                    print(f"Successfully refreshed RSS cache for topic: {topic}")
                    return fresh_data
                else:
                    # Fresh fetch failed, use cached data
                    print(f"Fresh RSS fetch failed for topic: {topic}, using cached data")
                    return cached_data

            # No cache or cache too old - must fetch fresh
            print(f"No RSS cache for topic: {topic}, fetching fresh data")
            fresh_data = self._fetch_from_rss_uncached(topic)
            
            if fresh_data and fresh_data.get('results'):
                # Update cache with fresh data
                with self.cache_lock:
                    self.news_cache[cache_key] = fresh_data
                    self.news_cache_last_updated[cache_key] = time.time()
                print(f"Successfully cached fresh RSS data for topic: {topic}")
                return fresh_data

            # Fresh fetch failed, try to use any cached data regardless of age
            if cached_data:
                print(f"RSS fetch failed for topic: {topic}, using stale cached data")
                return cached_data

            # Everything failed, return empty response
            print(f"No RSS data available for topic: {topic}")
            return {"results": []}

        except Exception as e:
            print(f"Error in RSS fetch for topic {topic}: {str(e)}")
            
            # If any error occurs, try to use cache as fallback
            if 'cached_data' in locals() and cached_data:
                print(f"Using cached RSS data after error for topic: {topic}")
                return cached_data
            
            # If everything fails, return empty response
            return {"results": []}

    def _refresh_rss_topic(self, topic):
        """Refresh RSS data for a specific topic in background"""
        try:
            cache_key = f"rss_{topic.lower().replace(' & ', '_').replace(' ', '_')}"
            fresh_data = self._fetch_from_rss_uncached(topic)
            
            if fresh_data and fresh_data.get('results'):
                with self.cache_lock:
                    self.news_cache[cache_key] = fresh_data
                    self.news_cache_last_updated[cache_key] = time.time()
                print(f"Background refresh completed for RSS topic: {topic}")
            else:
                print(f"Background refresh failed for RSS topic: {topic}")
        except Exception as e:
            print(f"Error in background RSS refresh for topic {topic}: {str(e)}")

    def _fetch_from_rss_uncached(self, topic):
        """
        Fetch articles from RSS feeds for a given topic without caching.
        Provides comprehensive parsing of RSS content including full text extraction.
        
        Args:
            topic (str): Topic to fetch RSS feeds for
            
        Returns:
            dict: Formatted response similar to NewsData API
        """
        topic_lower = topic.lower() if topic else ''
        
        # Check if we have RSS feeds configured for this topic
        if topic_lower not in self.rss_feeds:
            print(f"No RSS feeds configured for topic: {topic}")
            return {"results": []}
        
        print(f"Fetching articles from RSS feeds for topic: {topic}")
        all_articles = []
        
        # Process each RSS feed for the topic
        for feed_config in self.rss_feeds[topic_lower]:
            feed_url = feed_config['url']
            feed_name = feed_config['name']
            feed_description = feed_config.get('description', '')
            
            try:
                print(f"Parsing RSS feed: {feed_name} ({feed_url})")
                
                # Parse the RSS feed
                feed = feedparser.parse(feed_url)
                
                if feed.bozo:
                    print(f"Warning: RSS feed may have issues: {feed.bozo_exception}")
                
                # Extract feed metadata
                feed_title = getattr(feed.feed, 'title', feed_name)
                feed_link = getattr(feed.feed, 'link', '')
                feed_desc = getattr(feed.feed, 'description', feed_description)
                
                print(f"Feed parsed successfully: {len(feed.entries)} entries found")
                
                # Process each entry in the feed
                for entry in feed.entries[:20]:  # Process up to 20 entries
                    try:
                        # Extract basic information
                        title = getattr(entry, 'title', 'No Title')
                        link = getattr(entry, 'link', '')
                        
                        # Handle publication date
                        pub_date = None
                        if hasattr(entry, 'published_parsed') and entry.published_parsed:
                            pub_date = datetime(*entry.published_parsed[:6]).strftime('%Y-%m-%d %H:%M:%S')
                        elif hasattr(entry, 'updated_parsed') and entry.updated_parsed:
                            pub_date = datetime(*entry.updated_parsed[:6]).strftime('%Y-%m-%d %H:%M:%S')
                        elif hasattr(entry, 'published'):
                            # Try to parse the published string
                            try:
                                from dateutil import parser
                                parsed_date = parser.parse(entry.published)
                                pub_date = parsed_date.strftime('%Y-%m-%d %H:%M:%S')
                            except:
                                pub_date = entry.published
                        
                        if not pub_date:
                            pub_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        
                        # Extract content with multiple fallbacks
                        content = ""
                        description = ""
                        
                        # Try to get full content
                        if hasattr(entry, 'content') and entry.content:
                            # Handle multiple content entries
                            if isinstance(entry.content, list):
                                content = entry.content[0].value if entry.content else ""
                            else:
                                content = entry.content.value if hasattr(entry.content, 'value') else str(entry.content)
                        
                        # Fallback to summary/description
                        if not content and hasattr(entry, 'summary'):
                            content = entry.summary
                        
                        if hasattr(entry, 'description'):
                            description = entry.description
                        elif hasattr(entry, 'summary'):
                            description = entry.summary
                        
                        # Clean and process HTML content
                        if content:
                            # Parse HTML content
                            soup = BeautifulSoup(content, 'html.parser')
                            
                            # Remove script and style elements
                            for script in soup(["script", "style"]):
                                script.decompose()
                            
                            # Extract text content
                            text_content = soup.get_text()
                            
                            # Clean up whitespace
                            lines = (line.strip() for line in text_content.splitlines())
                            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                            content = ' '.join(chunk for chunk in chunks if chunk)
                            
                            # Limit content length for API compatibility
                            if len(content) > 2000:
                                content = content[:2000] + "..."
                        
                        # Clean description similarly
                        if description:
                            soup = BeautifulSoup(description, 'html.parser')
                            description = soup.get_text().strip()
                            if len(description) > 500:
                                description = description[:500] + "..."
                        
                        # Extract image URL with enhanced methods
                        image_url = None
                        
                        # Method 1: Try RSS media content
                        if hasattr(entry, 'media_content') and entry.media_content:
                            for media in entry.media_content:
                                if media.get('type', '').startswith('image/'):
                                    image_url = media.get('url')
                                    break
                        
                        # Method 2: Try RSS enclosures
                        if not image_url and hasattr(entry, 'enclosures'):
                            for enclosure in entry.enclosures:
                                if enclosure.get('type', '').startswith('image/'):
                                    image_url = enclosure.get('href')
                                    break
                        
                        # Method 3: Look for images in content HTML
                        if not image_url and content:
                            soup = BeautifulSoup(content, 'html.parser')
                            img_tags = soup.find_all('img')
                            for img_tag in img_tags:
                                src = img_tag.get('src')
                                if src and not src.startswith('data:'):  # Skip base64 images
                                    # Prefer larger images
                                    width = img_tag.get('width')
                                    height = img_tag.get('height')
                                    if width and height:
                                        try:
                                            if int(width) >= 200 or int(height) >= 200:
                                                image_url = src
                                                break
                                        except:
                                            pass
                                    else:
                                        image_url = src  # Use first image if no dimensions
                                        break
                        
                        # Method 4: Look for images in description HTML
                        if not image_url and description:
                            soup = BeautifulSoup(description, 'html.parser')
                            img_tag = soup.find('img')
                            if img_tag and img_tag.get('src'):
                                src = img_tag.get('src')
                                if not src.startswith('data:'):
                                    image_url = src
                        
                        # Method 5: Try to extract image from the article URL (web scraping)
                        if not image_url and link:
                            try:
                                image_url = self._extract_image_from_url(link)
                            except Exception as e:
                                print(f"Error extracting image from URL {link}: {str(e)}")
                        
                        # Method 6: Use topic-based fallback images if no image found
                        if not image_url:
                            image_url = self._get_fallback_image(topic_lower, feed_name)
                        
                        # Clean up image URL
                        if image_url:
                            # Make relative URLs absolute
                            if image_url.startswith('//'):
                                image_url = 'https:' + image_url
                            elif image_url.startswith('/') and feed_link:
                                image_url = urljoin(feed_link, image_url)
                            
                            # Improve image resolution if possible
                            image_url = self._improve_image_url(image_url)
                        
                        # Extract author information
                        author = ""
                        if hasattr(entry, 'author'):
                            author = entry.author
                        elif hasattr(entry, 'authors') and entry.authors:
                            author = entry.authors[0].get('name', '') if isinstance(entry.authors[0], dict) else str(entry.authors[0])
                        
                        # Extract categories/tags
                        categories = []
                        if hasattr(entry, 'tags'):
                            categories = [tag.term for tag in entry.tags if hasattr(tag, 'term')]
                        
                        # Create article object in NewsData API format
                        article = {
                            'article_id': hashlib.md5(link.encode()).hexdigest() if link else hashlib.md5(title.encode()).hexdigest(),
                            'title': title,
                            'link': link,
                            'keywords': categories,
                            'creator': [author] if author else None,
                            'video_url': None,
                            'description': description or content[:200] + "..." if content else "No description available",
                            'content': content,
                            'pubDate': pub_date,
                            'image_url': image_url,
                            'source_id': feed_name.lower().replace(' ', '_'),
                            'source_priority': 1,
                            'source_url': feed_link,
                            'source_icon': None,
                            'language': 'english',
                            'country': ['united states'],
                            'category': [topic_lower],
                            'ai_tag': 'ONLY AVAILABLE IN PROFESSIONAL AND CORPORATE PLANS',
                            'sentiment': 'ONLY AVAILABLE IN PROFESSIONAL AND CORPORATE PLANS',
                            'sentiment_stats': 'ONLY AVAILABLE IN PROFESSIONAL AND CORPORATE PLANS',
                            'ai_region': 'ONLY AVAILABLE IN CORPORATE PLANS',
                            'ai_org': 'ONLY AVAILABLE IN CORPORATE PLANS',
                            'duplicate': False
                        }
                        
                        # Add source name for display
                        article['source_name'] = feed_name
                        
                        all_articles.append(article)
                        
                    except Exception as e:
                        print(f"Error processing RSS entry: {str(e)}")
                        continue
                
                print(f"Successfully parsed {len([a for a in all_articles if a.get('source_name') == feed_name])} articles from {feed_name}")
                
            except Exception as e:
                print(f"Error parsing RSS feed {feed_name}: {str(e)}")
                continue
        
        # Sort articles by publication date (most recent first)
        try:
            all_articles.sort(key=lambda x: x.get('pubDate', ''), reverse=True)
        except Exception as e:
            print(f"Error sorting articles by date: {str(e)}")
        
        print(f"Total RSS articles fetched: {len(all_articles)}")
        
        return {
            "status": "success",
            "totalResults": len(all_articles),
            "results": all_articles,
            "nextPage": None
        }

    def _get_topic_domains(self):
        """Helper method to centralize topic domain definitions"""
        return {
            'technology': [
                'wired.com',              # Prioritized as requested
                'theverge.com',
                'techcrunch.com',
                'arstechnica.com'
            ],
            'science': [
                'scientificamerican.com',  # Confirmed working (3 articles)
                'popsci.com',              # Popular Science (10 articles)
                'space.com',               # Space news (10 articles)
                'phys.org'                 # Physics and other sciences (10 articles)
            ],
            'business': [
                'finance.yahoo.com',       # Free financial news (5 articles)
                'forbes.com',              # Working domain (10 articles)
                'cnbc.com',                # Working domain (10 articles)
                'fortune.com'              # Business news (6 articles)
            ],
            'politics': [
                'politico.com',            # Working domain (6 articles)
                'thehill.com',             # Working domain (10 articles)
                'axios.com',               # Working domain (10 articles)
                'nbcnews.com',             # Working domain (10 articles)
                'edition.cnn.com',         # Working domain (10 articles)
                'theguardian.com'          # Working domain (10 articles)
            ],
            'world': [
                'aljazeera.com',           # Working domain (10 articles)
                'bbc.com',                 # Working domain (10 articles)
                'france24.com',            # Working domain (10 articles)
                'abc.net.au'               # Working domain (10 articles)
                # Removed: voanews.com (not in NewsData database)
            ],
            'sports': [
                'espn.com',                # Working domain (10 articles)
                'skysports.com',           # Working domain (10 articles)
                'nbcsports.com',           # Working domain (10 articles)
                'cbssports.com'            # Working domain (10 articles)
            ],
            'entertainment': [
                'variety.com',             # Working domain (10 articles)
                'hollywoodreporter.com',   # Working domain (10 articles)
                'deadline.com',            # Working domain (10 articles)
                'billboard.com',           # Music industry news (10 articles)
                'rollingstone.com'         # Working domain (10 articles)
            ],
            'health': [
                # All previous health sources removed as they don't exist in NewsData database
                # Will fall back to generic health queries for this topic
            ]
        }

    def get_articles(self, topic=None, days=3):
        """
        Get articles from NewsData.io based on topic and date range.
        First checks the cache, then falls back to API if needed.

        Args:
            topic (str): Topic to search for
            days (int): Number of days to look back

        Returns:
            dict: JSON response from NewsData.io API
        """
        try:
            # Create cache key
            cache_key = self._create_cache_key(topic)

            # Check if there's already an active request for this topic
            with self.request_lock:
                if cache_key in self.active_requests:
                    print(f"Request already in progress for topic: {topic}, waiting for completion...")
                    # Wait for the active request to complete
                    active_request = self.active_requests[cache_key]
            
            # If there was an active request, wait for it and then check cache
            if 'active_request' in locals():
                active_request.wait()  # Wait for the other request to complete
                # Now check cache again as it should be updated
                with self.cache_lock:
                    cached_data = self.news_cache.get(cache_key)
                    if cached_data:
                        print(f"Using cache updated by concurrent request for topic: {topic}")
                        return cached_data

            # Try to get from cache first
            with self.cache_lock:
                cached_data = self.news_cache.get(cache_key)
                last_updated = self.news_cache_last_updated.get(cache_key, 0)

            # Check if cache is valid
            current_time = time.time()
            cache_age = current_time - last_updated

            # Fast path: Fresh cache (under 2 hours)
            if cached_data and cache_age < self.cache_fresh_period:
                print(f"Fresh cache hit for topic: {topic} (age: {cache_age/60:.1f} min)")
                return cached_data

            # Tier 2: Stale but usable cache (2-12 hours) - use cache but refresh in background
            if cached_data and cache_age < self.cache_stale_period:
                print(f"Stale cache hit for topic: {topic} (age: {cache_age/3600:.1f} hours), refreshing in background")

                # Only start background refresh if we're not rate limited and no request is active
                with self.api_quota_lock:
                    if not self.is_rate_limited and self.api_calls_today < self.daily_limit * 0.9:
                        with self.request_lock:
                            if cache_key not in self.active_requests:
                                # Start background refresh without waiting for result
                                threading.Thread(target=self._refresh_topic, args=(topic,), daemon=True).start()

                # Return cached data immediately
                return cached_data

            # Tier 3: Very stale cache (12-24 hours) - still usable in emergency
            if cached_data and cache_age < self.cache_max_period:
                print(f"Very stale cache hit for topic: {topic} (age: {cache_age/3600:.1f} hours)")

                # Try to refresh immediately if not rate limited
                with self.api_quota_lock:
                    if not self.is_rate_limited:
                        try:
                            # Check if request is already active before starting new one
                            with self.request_lock:
                                if cache_key not in self.active_requests:
                                    # Try a direct refresh
                                    if self._refresh_topic(topic):
                                        # Refresh successful, get updated cache
                                        with self.cache_lock:
                                            updated_cache = self.news_cache.get(cache_key)
                                        if updated_cache:
                                            return updated_cache
                        except Exception as e:
                            print(f"Failed to refresh very stale cache: {str(e)}")

                # If refresh failed or rate limited, use stale cache
                return cached_data

            # Check if we're rate limited
            with self.api_quota_lock:
                if self.is_rate_limited:
                    # If rate limited, use any cache we have regardless of age
                    if cached_data:
                        print(f"Using stale cache due to API rate limiting")
                        return cached_data

                    # No cache available
                    return {"results": [], "status": "error", "message": "API rate limited"}

            # If we get here, we need to refresh from API directly
            # Check if request is already active before starting new one
            with self.request_lock:
                if cache_key in self.active_requests:
                    print(f"Another request started for topic: {topic} while we were checking cache, waiting...")
                    active_request = self.active_requests[cache_key]
                    active_request.wait()
                    # Check cache again after waiting
                    with self.cache_lock:
                        updated_cache = self.news_cache.get(cache_key)
                    if updated_cache:
                        return updated_cache

            print(f"No valid cache for topic: {topic}, fetching from API")
            
            # Mark this request as active
            request_event = threading.Event()
            with self.request_lock:
                self.active_requests[cache_key] = request_event

            try:
                api_response = self._fetch_from_api(topic)

                # If successful, update cache and return
                if api_response and api_response.get('results'):
                    with self.cache_lock:
                        self.news_cache[cache_key] = api_response
                        self.news_cache_last_updated[cache_key] = time.time()
                    return api_response

                # API failed but we have a cache, use it regardless of age
                if cached_data:
                    print(f"API returned no articles, using cached data")
                    return cached_data

                # Everything failed, return empty response
                return {"results": []}
            
            finally:
                # Always clean up the active request tracking
                with self.request_lock:
                    if cache_key in self.active_requests:
                        self.active_requests[cache_key].set()  # Signal completion
                        del self.active_requests[cache_key]

        except Exception as e:
            print(f"Error in get_articles: {str(e)}")

            # Clean up active request tracking on error
            try:
                with self.request_lock:
                    if cache_key in self.active_requests:
                        self.active_requests[cache_key].set()
                        del self.active_requests[cache_key]
            except:
                pass

            # If any error occurs, try to use cache as fallback
            if 'cached_data' in locals() and cached_data:
                print(f"Using cached data after error")
                return cached_data

            # If everything fails, return empty response
            return {"results": []}

    def map_topic_to_query(self, topic):
        """
        Map the frontend topic to a search query for NewsData.io API.
        Uses specific keywords for each topic to improve quality of results.

        Args:
            topic (str): Topic from the frontend

        Returns:
            dict: Parameters for NewsData.io API
        """
        # Add safety check for None values
        if topic is None or topic == '':
            return {"category": "top"}

        topic_lower = topic.lower()

        # Define common parameters for all topic queries
        common_params = {
            "language": "en",
            "country": "us,gb,ca,au"  # Include English-speaking countries
        }

        # Define topic-specific parameters with dedicated keywords
        query_keywords = {
            'technology': "technology OR tech OR innovation OR artificial intelligence OR AI OR software",
            'science': "science OR research OR discovery OR scientific OR study",
            'business': "business OR finance OR economy OR market OR investment",
            'politics': "politics OR government OR policy OR election",
            'world': "world OR international OR global",
            'sports': "sports OR athletics OR championship OR tournament OR game",
            'entertainment': "entertainment OR movies OR music OR celebrity OR film",
            'health': "health OR medicine OR wellness OR healthcare OR medical",
            'newsletters': "newsletter OR engineering OR technology OR industry OR analysis"
        }

        # If we have specific keywords for this topic, use them
        if topic_lower in query_keywords:
            # Create the parameters for the API request
            params = common_params.copy()
            params["category"] = topic_lower  # Use the topic as category
            params["q"] = query_keywords.get(topic_lower)  # Use topic-specific keywords

            print(f"Using specialized query parameters for {topic_lower}")
            return params

        # Fallback to traditional category-based approach
        elif topic_lower in ['top', 'general']:
            params = common_params.copy()
            params["category"] = "top"
            return params
        else:
            # For any unrecognized topic, default to keyword search
            params = common_params.copy()
            params["q"] = topic_lower
            return params

    def get_article_content(self, url):
        """
        Extract the content of an article from its URL using newspaper3k.

        Args:
            url (str): The URL of the article

        Returns:
            str: The extracted article content or empty string if extraction failed
        """
        try:
            # Download and parse the article
            article = Article(url)
            article.download()
            article.parse()

            # Get the article content
            content = article.text

            # If content is too short, also include the title and description
            if len(content) < 100 and article.title:
                content = f"{article.title}\n\n{article.meta_description or ''}\n\n{content}"

            return content
        except Exception as e:
            print(f"Error extracting content from {url}: {str(e)}")
            return ""

    def ensure_source_diversity(self, response):
        """
        Process articles to ensure diversity while removing duplicates.
        Only filters out news aggregators, keeping all actual news sources.

        Args:
            response (dict): The NewsData.io API response
        """
        if not response or 'results' not in response or not response['results']:
            return

        articles = response['results']
        print(f"Processing {len(articles)} articles from API")

        # Track all seen articles by URL to eliminate duplicates
        seen_urls = set()
        seen_titles = set()

        # Only filter out aggregators like Google News
        filtered_sources = [
            'news.google.com', 'google'
        ]

        valid_articles = []

        for article in articles:
            source_id = article.get('source_id', '').lower() if article.get('source_id') else ''
            source_name = article.get('source_name', '').lower() if article.get('source_name') else ''
            source_url = article.get('source_url', '').lower() if article.get('source_url') else ''
            article_url = article.get('link', '')
            article_title = article.get('title', '')

            # Skip duplicate URLs
            if article_url in seen_urls:
                continue

            # Skip duplicate titles
            if article_title in seen_titles:
                continue

            # Skip articles from filtered sources (only aggregators)
            if any(filtered_source in source_id or filtered_source in source_name or filtered_source in source_url
                   for filtered_source in filtered_sources):
                continue

            # Mark this URL and title as seen to avoid duplicates
            seen_urls.add(article_url)
            seen_titles.add(article_title)

            # Try to improve image resolution for all articles
            if article.get('image_url'):
                self._improve_image_resolution(article)

            # Add to valid articles
            valid_articles.append(article)

        # Sort all articles by recency
        valid_articles.sort(
            key=lambda x: x.get('pubDate', ''),
            reverse=True
        )

        # Replace the articles in the response
        response['results'] = valid_articles

        # Log the distribution of sources
        source_distribution = {}
        for article in valid_articles:
            source = article.get('source_name', article.get('source_id', 'unknown'))
            source_distribution[source] = source_distribution.get(source, 0) + 1

        print(f"Total articles after processing: {len(valid_articles)} (from {len(articles)} original articles)")
        print(f"Source distribution: {source_distribution}")

    def _improve_image_resolution(self, article):
        """Helper method to improve image resolution for an article"""
        image_url = article['image_url']
        # Common patterns for image resizing in URLs
        image_url = image_url.replace('&width=300', '&width=1200')
        image_url = image_url.replace('&height=200', '&height=800')
        image_url = image_url.replace('width=300', 'width=1200')
        image_url = image_url.replace('w=300', 'w=1200')
        image_url = image_url.replace('h=200', 'h=800')
        # Update the article with potentially higher-res image
        article['image_url'] = image_url

    def _extract_image_from_url(self, url):
        """Extract the main image from an article URL using web scraping"""
        try:
            # Set a timeout and user agent to avoid being blocked
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Try Open Graph image first (most reliable)
                og_image = soup.find('meta', property='og:image')
                if og_image and og_image.get('content'):
                    return og_image['content']
                
                # Try Twitter card image
                twitter_image = soup.find('meta', attrs={'name': 'twitter:image'})
                if twitter_image and twitter_image.get('content'):
                    return twitter_image['content']
                
                # Try to find the main article image
                article_selectors = [
                    'article img',
                    '.article-content img',
                    '.post-content img',
                    '.entry-content img',
                    'main img'
                ]
                
                for selector in article_selectors:
                    img = soup.select_one(selector)
                    if img and img.get('src'):
                        src = img['src']
                        if not src.startswith('data:'):
                            # Make absolute URL
                            if src.startswith('//'):
                                return 'https:' + src
                            elif src.startswith('/'):
                                return urljoin(url, src)
                            else:
                                return src
                
                # Fallback: find any large image
                all_imgs = soup.find_all('img')
                for img in all_imgs:
                    src = img.get('src')
                    if src and not src.startswith('data:'):
                        width = img.get('width')
                        height = img.get('height')
                        if width and height:
                            try:
                                if int(width) >= 300 or int(height) >= 200:
                                    if src.startswith('//'):
                                        return 'https:' + src
                                    elif src.startswith('/'):
                                        return urljoin(url, src)
                                    else:
                                        return src
                            except:
                                pass
                
        except Exception as e:
            print(f"Error scraping image from {url}: {str(e)}")
        
        return None

    def _get_fallback_image(self, topic_lower, feed_name):
        """Get a fallback image based on topic and feed name"""
        # Topic-based fallback images (using Unsplash for high-quality, free images)
        fallback_images = {
            'technology': 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=600&fit=crop',
            'business': 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=600&fit=crop',
            'science': 'https://images.unsplash.com/photo-1532094349884-543bc11b234d?w=800&h=600&fit=crop',
            'design': 'https://images.unsplash.com/photo-1558655146-d09347e92766?w=800&h=600&fit=crop',
            'marketing': 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop',
            'ai & machine learning': 'https://images.unsplash.com/photo-1555255707-c07966088b7b?w=800&h=600&fit=crop',
            'startups': 'https://images.unsplash.com/photo-1559136555-9303baea8ebd?w=800&h=600&fit=crop',
            'finance': 'https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?w=800&h=600&fit=crop'
        }
        
        # Feed-specific fallback images for known sources
        feed_images = {
            'the pragmatic engineer': 'https://images.unsplash.com/photo-1517077304055-6e89abbf09b0?w=800&h=600&fit=crop',
            'venturebeat': 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=600&fit=crop',
            'harvard business review': 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=600&fit=crop',
            'nature': 'https://images.unsplash.com/photo-1532094349884-543bc11b234d?w=800&h=600&fit=crop',
            'science magazine': 'https://images.unsplash.com/photo-1507413245164-6160d8298b31?w=800&h=600&fit=crop',
            'smashing magazine': 'https://images.unsplash.com/photo-1558655146-d09347e92766?w=800&h=600&fit=crop',
            'designboom': 'https://images.unsplash.com/photo-1541462608143-67571c6738dd?w=800&h=600&fit=crop',
            'hubspot marketing blog': 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop',
            'content marketing institute': 'https://images.unsplash.com/photo-1533750516457-a7f992034fec?w=800&h=600&fit=crop',
            "o'reilly radar": 'https://images.unsplash.com/photo-1555255707-c07966088b7b?w=800&h=600&fit=crop',
            'openai blog': 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&h=600&fit=crop',
            'techcrunch': 'https://images.unsplash.com/photo-1559136555-9303baea8ebd?w=800&h=600&fit=crop',
            'entrepreneur': 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=600&fit=crop',
            'investopedia': 'https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?w=800&h=600&fit=crop',
            'the motley fool': 'https://images.unsplash.com/photo-1590283603385-17ffb3a7f29f?w=800&h=600&fit=crop'
        }
        
        # Try feed-specific image first
        feed_key = feed_name.lower()
        if feed_key in feed_images:
            return feed_images[feed_key]
        
        # Fallback to topic-based image
        if topic_lower in fallback_images:
            return fallback_images[topic_lower]
        
        # Default fallback image
        return 'https://images.unsplash.com/photo-1504711434969-e33886168f5c?w=800&h=600&fit=crop'

    def _improve_image_url(self, image_url):
        """Improve image URL for better resolution and quality"""
        if not image_url:
            return image_url
        
        # Unsplash image optimization
        if 'unsplash.com' in image_url:
            # Ensure we have good dimensions
            if 'w=' not in image_url:
                separator = '&' if '?' in image_url else '?'
                image_url += f'{separator}w=800&h=600&fit=crop&auto=format'
        
        # Medium image optimization
        elif 'medium.com' in image_url or 'cdn-images' in image_url:
            # Replace small image sizes with larger ones
            image_url = image_url.replace('/max/600/', '/max/1200/')
            image_url = image_url.replace('/max/800/', '/max/1200/')
        
        # WordPress/blog image optimization
        elif any(domain in image_url for domain in ['wordpress.com', 'wp.com', 'gravatar.com']):
            if '?w=' in image_url:
                image_url = re.sub(r'\?w=\d+', '?w=800', image_url)
            elif '?' in image_url:
                image_url += '&w=800'
            else:
                image_url += '?w=800'
        
        # Generic image size improvements
        else:
            # Common patterns for image resizing
            replacements = [
                (r'_small\.', '_large.'),
                (r'_thumb\.', '_large.'),
                (r'_150x150\.', '_800x600.'),
                (r'_300x200\.', '_800x600.'),
                (r'/thumb/', '/large/'),
                (r'/small/', '/large/'),
                (r'width=300', 'width=800'),
                (r'height=200', 'height=600'),
                (r'w=300', 'w=800'),
                (r'h=200', 'h=600')
            ]
            
            for pattern, replacement in replacements:
                image_url = re.sub(pattern, replacement, image_url)
        
        return image_url