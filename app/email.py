import os
from sendgrid import SendGridAP<PERSON>lient
from sendgrid.helpers.mail import Mail
from flask import url_for, current_app

def send_verification_email(user):
    """
    Send a verification email to the user with a link to verify their email address
    using SendGrid API.

    Args:
        user: The user object containing email and verification token
    """
    # Get email configuration from environment variables
    api_key = os.environ.get('SENDGRID_API_KEY') or os.environ.get('MAIL_PASSWORD')  # Try both environment variables

    # Create verification URL
    # Check if custom domain is set in environment variables
    custom_domain = os.environ.get('CUSTOM_DOMAIN')

    if custom_domain:
        # Use custom domain for verification URL
        scheme = os.environ.get('PREFERRED_URL_SCHEME', 'https')
        # Make sure the URL is correctly formed with a leading slash
        token_path = url_for('main.verify_email', token=user.verification_token)
        # Remove any domain part if present (in case _external was True)
        if '://' in token_path:
            token_path = '/' + token_path.split('/', 3)[3]
        verification_url = f"{scheme}://{custom_domain}{token_path}"
    else:
        # Fall back to default URL generation
        verification_url = url_for(
            'main.verify_email',
            token=user.verification_token,
            _external=True
        )

    # Create HTML content with verification link
    html_content = f"""
    <html>
    <body>
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">Welcome to BIRDII!</h2>
            <p>Thank you for registering. Please verify your email address by clicking the button below:</p>
            <p style="text-align: center; margin: 30px 0;">
                <a href="{verification_url}"
                   style="background-color: #007bff; color: white; padding: 10px 20px;
                          text-decoration: none; border-radius: 5px; font-weight: bold;">
                    Verify Email Address
                </a>
            </p>
            <p>If the button doesn't work, you can also copy and paste the following link into your browser:</p>
            <p style="word-break: break-all;">{verification_url}</p>
            <p>This link will expire in 24 hours.</p>
            <p>If you didn't register for BIRDII, please ignore this email.</p>
            <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
            <p style="color: #777; font-size: 12px;">
                BIRDII - News with context, knowledge with every read
            </p>
        </div>
    </body>
    </html>
    """

    # Get sender email from environment variables
    from_email = os.environ.get('MAIL_DEFAULT_SENDER')

    # <AUTHOR> <EMAIL>")
    if from_email and '<' in from_email and '>' in from_email:
        from_email = from_email.split('<')[1].split('>')[0]
    elif not from_email:
        # Fallback to a default if not set
        from_email = "<EMAIL>"
        current_app.logger.warning("MAIL_DEFAULT_SENDER not set, using default: <EMAIL>")

    # Create SendGrid message object
    message = Mail(
        from_email=from_email,
        to_emails=user.email,
        subject='BIRDII - Verify Your Email Address',
        html_content=html_content
    )

    # Print the verification URL to console only in development mode
    if current_app.config.get('DEBUG') or os.environ.get('FLASK_ENV') == 'development':
        print(f"\n==== VERIFICATION URL (for development) ====\n{verification_url}\n===========================================\n")

    # Check if we're in development mode - only skip sending if explicitly in dev mode
    # If FORCE_EMAIL_SEND is true, we'll send emails even in development mode
    if ((current_app.config.get('DEBUG') is True or os.environ.get('FLASK_ENV') == 'development') \
        and os.environ.get('FORCE_EMAIL_SEND') != 'true'):
        # In development, just log the email and don't actually send it
        current_app.logger.info(f"[DEV MODE] Would send verification email to {user.email}")
        return True

    try:
        # Send email using SendGrid API
        sg = SendGridAPIClient(api_key)
        response = sg.send(message)

        # Log success
        current_app.logger.info(f"Verification email sent to {user.email}. Status code: {response.status_code}")
        return True
    except Exception as e:
        current_app.logger.error(f"Failed to send verification email: {str(e)}")

        # If SendGrid fails, fall back to printing the URL in development mode
        if current_app.config.get('DEBUG') or os.environ.get('FLASK_ENV') == 'development':
            print(f"\n==== EMAIL SENDING FAILED - USE THIS URL ====\n{verification_url}\n===========================================\n")
            # Return True in development so the flow isn't interrupted
            return True
        else:
            # In production, return False to indicate failure
            return False

# Fallback function that uses SMTP if SendGrid is not configured
def send_verification_email_smtp(user):
    """
    Fallback function to send verification email via SMTP.
    Used if SendGrid is not configured.
    """
    import smtplib
    from email.mime.text import MIMEText
    from email.mime.multipart import MIMEMultipart

    # Get email configuration from environment variables
    sender_email = os.environ.get('MAIL_USERNAME')
    sender_password = os.environ.get('MAIL_PASSWORD')
    mail_server = os.environ.get('MAIL_SERVER', 'smtp.gmail.com')
    mail_port = int(os.environ.get('MAIL_PORT', 587))

    # Create verification URL
    # Check if custom domain is set in environment variables
    custom_domain = os.environ.get('CUSTOM_DOMAIN')

    if custom_domain:
        # Use custom domain for verification URL
        scheme = os.environ.get('PREFERRED_URL_SCHEME', 'https')
        # Make sure the URL is correctly formed with a leading slash
        token_path = url_for('main.verify_email', token=user.verification_token)
        # Remove any domain part if present (in case _external was True)
        if '://' in token_path:
            token_path = '/' + token_path.split('/', 3)[3]
        verification_url = f"{scheme}://{custom_domain}{token_path}"
    else:
        # Fall back to default URL generation
        verification_url = url_for(
            'main.verify_email',
            token=user.verification_token,
            _external=True
        )

    # Create message
    msg = MIMEMultipart()

    # Get sender email from environment variables
    from_email = os.environ.get('MAIL_DEFAULT_SENDER')
    if not from_email:
        # Fallback to a default if not set
        from_email = "BIRDII <<EMAIL>>"
        current_app.logger.warning("MAIL_DEFAULT_SENDER not set, using default: BIRDII <<EMAIL>>")

    msg['From'] = from_email
    msg['To'] = user.email
    msg['Subject'] = 'BIRDII - Verify Your Email Address'

    # Create HTML body with verification link
    body = f"""
    <html>
    <body>
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">Welcome to BIRDII!</h2>
            <p>Thank you for registering. Please verify your email address by clicking the button below:</p>
            <p style="text-align: center; margin: 30px 0;">
                <a href="{verification_url}"
                   style="background-color: #007bff; color: white; padding: 10px 20px;
                          text-decoration: none; border-radius: 5px; font-weight: bold;">
                    Verify Email Address
                </a>
            </p>
            <p>If the button doesn't work, you can also copy and paste the following link into your browser:</p>
            <p style="word-break: break-all;">{verification_url}</p>
            <p>This link will expire in 24 hours.</p>
            <p>If you didn't register for BIRDII, please ignore this email.</p>
            <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
            <p style="color: #777; font-size: 12px;">
                BIRDII - News with context, knowledge with every read
            </p>
        </div>
    </body>
    </html>
    """

    msg.attach(MIMEText(body, 'html'))

    try:
        # Connect to mail server
        server = smtplib.SMTP(mail_server, mail_port)
        server.starttls()
        server.login(sender_email, sender_password)

        # Send email
        text = msg.as_string()

        # Extract email from sender format for SMTP sendmail
        smtp_from_email = from_email
        if '<' in from_email and '>' in from_email:
            smtp_from_email = from_email.split('<')[1].split('>')[0]

        server.sendmail(smtp_from_email, user.email, text)
        server.quit()

        # For development, also print the verification URL to console
        if current_app.config.get('DEBUG', False):
            print(f"\nVerification URL (for development): {verification_url}\n")

        return True
    except Exception as e:
        current_app.logger.error(f"Failed to send verification email via SMTP: {str(e)}")
        return False
