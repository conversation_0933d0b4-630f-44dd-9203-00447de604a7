import requests
import os
import json
import threading
import time
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Any, Optional
from flask import has_app_context, current_app
from dotenv import load_dotenv

# YouTube Transcript API
try:
    from youtube_transcript_api import YouTubeTranscriptApi
    from youtube_transcript_api.formatters import TextFormatter
    from youtube_transcript_api._errors import RequestBlocked, TranscriptsDisabled
    TRANSCRIPT_API_AVAILABLE = True
except ImportError:
    TRANSCRIPT_API_AVAILABLE = False
    print("⚠️ youtube-transcript-api not available. Install with: pip install youtube-transcript-api")

# Load environment variables
load_dotenv()

class YouTubeService:
    _background_worker_started = False  # Class-level flag to prevent multiple workers
    _worker_lock = threading.Lock()     # Thread-safe singleton
    
    def __init__(self):
        self.api_key = os.environ.get('YOUTUBE_API_KEY')
        if not self.api_key:
            raise ValueError("YOUTUBE_API_KEY environment variable is not set. Please add it to your .env file.")
        
        self.base_url = "https://www.googleapis.com/youtube/v3"
        
        # Initialize YouTube Transcript API with proxy support for cloud deployment
        self.transcript_api = None
        if TRANSCRIPT_API_AVAILABLE:
            try:
                # Check if we're in a cloud environment and need proxy
                proxy_config = self._get_proxy_config()
                if proxy_config:
                    self.transcript_api = YouTubeTranscriptApi(proxy_config=proxy_config)
                    print("🌐 YouTube Transcript API initialized with proxy for cloud deployment")
                else:
                    self.transcript_api = YouTubeTranscriptApi()
                    print("📺 YouTube Transcript API initialized for local development")
            except Exception as e:
                print(f"⚠️ Failed to initialize YouTube Transcript API: {e}")
                self.transcript_api = None
        
        # YouTube channels configuration
        self.channels = {
            'bytebytego': {
                'id': 'UCZgt6AzoyjslHTC9dz0UoTw',
                'name': 'ByteByteGo',
                'description': 'System design and software engineering'
            }
        }
        
        # Video cache for storing pre-fetched videos
        self.video_cache = {}
        self.video_cache_last_updated = {}
        self.cache_lock = threading.Lock()
        self._cache_loaded = False
        
        # Transcript cache for storing extracted transcripts
        self.transcript_cache = {}
        self.transcript_cache_lock = threading.Lock()
        
        # Track refresh operations in progress to prevent duplicates
        self.refresh_in_progress = {}
        self.refresh_lock = threading.Lock()
        
        # Cache validity periods (in seconds) - longer than news articles
        self.cache_fresh_period = 43200   # 12 hours - considered fresh
        self.cache_stale_period = 86400   # 24 hours - still usable but refresh in background
        self.cache_max_period = 172800    # 48 hours - absolute maximum before forced refresh
        
        # Start background refresh worker only once (thread-safe)
        with YouTubeService._worker_lock:
            if not YouTubeService._background_worker_started:
                self.start_background_refresh()
                YouTubeService._background_worker_started = True
    
    def _get_proxy_config(self):
        """
        Get proxy configuration for cloud environments.
        Returns None for local development, proxy config for cloud deployment.
        """
        # Check for explicit proxy environment variables
        proxy_url = os.environ.get('YOUTUBE_PROXY_URL')
        if proxy_url:
            try:
                from youtube_transcript_api.proxies import GenericProxyConfig
                return GenericProxyConfig(
                    http_url=proxy_url,
                    https_url=proxy_url
                )
            except ImportError:
                print("⚠️ Proxy configuration not available in this version")
                return None
        
        # Check for Webshare proxy credentials (recommended for production)
        webshare_username = os.environ.get('WEBSHARE_PROXY_USERNAME')
        webshare_password = os.environ.get('WEBSHARE_PROXY_PASSWORD')
        if webshare_username and webshare_password:
            try:
                from youtube_transcript_api.proxies import WebshareProxyConfig
                return WebshareProxyConfig(
                    proxy_username=webshare_username,
                    proxy_password=webshare_password
                )
            except ImportError:
                print("⚠️ Webshare proxy configuration not available in this version")
                return None
        
        # Detect cloud environment and warn if no proxy is configured
        cloud_indicators = [
            'DYNO',  # Heroku
            'AWS_EXECUTION_ENV',  # AWS Lambda
            'GOOGLE_CLOUD_PROJECT',  # Google Cloud
            'AZURE_FUNCTIONS_ENVIRONMENT',  # Azure
            'VERCEL',  # Vercel
        ]
        
        is_cloud = any(os.environ.get(indicator) for indicator in cloud_indicators)
        if is_cloud:
            print("⚠️ Cloud environment detected but no proxy configured.")
            print("   YouTube may block requests. Consider setting up a proxy:")
            print("   - Set WEBSHARE_PROXY_USERNAME and WEBSHARE_PROXY_PASSWORD for Webshare")
            print("   - Or set YOUTUBE_PROXY_URL for a custom proxy")
        
        return None
    
    def start_background_refresh(self):
        """Start the background thread for refreshing video cache"""
        refresh_thread = threading.Thread(target=self._background_refresh_worker, daemon=True)
        refresh_thread.start()
    
    def _background_refresh_worker(self):
        """Background worker that refreshes video cache periodically"""
        while True:
            try:
                current_time = time.time()
                
                # Check each channel for cache refresh needs
                for channel_key in self.channels.keys():
                    cache_key = f"youtube_{channel_key}"
                    last_updated = self.video_cache_last_updated.get(cache_key, 0)
                    
                    # If cache is stale (older than 12 hours), refresh it
                    if current_time - last_updated > self.cache_fresh_period:
                        print(f"🔄 Background refresh: Updating {channel_key} videos...")
                        self._refresh_channel_atomic(channel_key)
                
                # Sleep for 1 hour before next check
                time.sleep(3600)
                
            except Exception as e:
                print(f"❌ Background refresh error: {e}")
                # Sleep for 30 minutes on error before retrying
                time.sleep(1800)
    
    def _refresh_channel(self, channel_key):
        """Refresh videos for a specific channel"""
        try:
            with self.cache_lock:
                cache_key = f"youtube_{channel_key}"
                videos = self._fetch_channel_videos(channel_key)
                
                if videos:
                    self.video_cache[cache_key] = videos
                    self.video_cache_last_updated[cache_key] = time.time()
                    return len(videos)  # Return count instead of printing
                else:
                    print(f"⚠️ No videos fetched for {channel_key}")
                    return 0
                    
        except Exception as e:
            print(f"❌ Error refreshing {channel_key}: {e}")
            return 0
    
    def _refresh_channel_atomic(self, channel_key):
        """Atomically refresh videos for a specific channel with proper locking"""
        with self.refresh_lock:
            if channel_key in self.refresh_in_progress:
                print(f"⏭️ Refresh already in progress for {channel_key}, skipping...")
                return False
            # Mark refresh as in progress
            self.refresh_in_progress[channel_key] = True
        
        try:
            video_count = self._refresh_channel(channel_key)
            if video_count > 0:
                print(f"✅ Refreshed {video_count} videos for {channel_key}")
            return video_count > 0
        finally:
            # Always clear the refresh flag when done
            with self.refresh_lock:
                self.refresh_in_progress.pop(channel_key, None)
    
    def get_channel_uploads_playlist_id(self, channel_id: str) -> str:
        """Get the uploads playlist ID for a channel"""
        url = f"{self.base_url}/channels"
        params = {
            'part': 'contentDetails',
            'id': channel_id,
            'key': self.api_key
        }
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            if 'items' in data and len(data['items']) > 0:
                uploads_playlist_id = data['items'][0]['contentDetails']['relatedPlaylists']['uploads']
                return uploads_playlist_id
            else:
                raise Exception(f"Channel not found: {channel_id}")
                
        except requests.exceptions.RequestException as e:
            raise Exception(f"Failed to get channel info: {e}")
    
    def get_playlist_videos(self, playlist_id: str, max_results: int = 10) -> List[Dict[str, Any]]:
        """Get videos from a playlist"""
        url = f"{self.base_url}/playlistItems"
        params = {
            'part': 'snippet',
            'playlistId': playlist_id,
            'maxResults': max_results,
            'order': 'date',
            'key': self.api_key
        }
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            return data.get('items', [])
            
        except requests.exceptions.RequestException as e:
            raise Exception(f"Failed to get playlist videos: {e}")
    
    def get_video_details(self, video_ids: List[str]) -> Dict[str, Dict[str, Any]]:
        """Get additional video details like view count, duration, etc."""
        if not video_ids:
            return {}
            
        url = f"{self.base_url}/videos"
        params = {
            'part': 'statistics,contentDetails',
            'id': ','.join(video_ids),
            'key': self.api_key
        }
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            video_details = {}
            if 'items' in data:
                for item in data['items']:
                    video_id = item['id']
                    video_details[video_id] = {
                        'view_count': item.get('statistics', {}).get('viewCount', 'N/A'),
                        'like_count': item.get('statistics', {}).get('likeCount', 'N/A'),
                        'duration': item.get('contentDetails', {}).get('duration', 'N/A')
                    }
            
            return video_details
            
        except requests.exceptions.RequestException as e:
            print(f"Warning: Could not fetch video details: {e}")
            return {}
    
    def format_duration(self, duration: str) -> str:
        """Convert ISO 8601 duration to readable format"""
        if duration == 'N/A':
            return duration
            
        # Simple parsing for PT#M#S format
        duration = duration.replace('PT', '')
        minutes = 0
        seconds = 0
        
        if 'M' in duration:
            parts = duration.split('M')
            minutes = int(parts[0])
            duration = parts[1] if len(parts) > 1 else ''
        
        if 'S' in duration:
            seconds = int(duration.replace('S', ''))
        
        return f"{minutes}:{seconds:02d}"
    
    def _fetch_channel_videos(self, channel_key: str, max_results: int = 10) -> List[Dict[str, Any]]:
        """Fetch videos from a specific channel"""
        if channel_key not in self.channels:
            raise ValueError(f"Unknown channel: {channel_key}")
        
        channel_info = self.channels[channel_key]
        channel_id = channel_info['id']
        
        try:
            # Step 1: Get uploads playlist ID
            uploads_playlist_id = self.get_channel_uploads_playlist_id(channel_id)
            
            # Step 2: Get videos from playlist
            playlist_items = self.get_playlist_videos(uploads_playlist_id, max_results)
            
            # Step 3: Extract video IDs for additional details
            video_ids = [item['snippet']['resourceId']['videoId'] for item in playlist_items]
            
            # Step 4: Get additional video details
            video_details = self.get_video_details(video_ids)
            
            # Step 5: Format the results
            videos = []
            for item in playlist_items:
                snippet = item['snippet']
                video_id = snippet['resourceId']['videoId']
                details = video_details.get(video_id, {})
                
                # Parse publish date
                publish_date = datetime.fromisoformat(snippet['publishedAt'].replace('Z', '+00:00'))
                
                video_info = {
                    'title': snippet['title'],
                    'description': snippet['description'],
                    'video_id': video_id,
                    'video_url': f"https://www.youtube.com/watch?v={video_id}",
                    'thumbnail_url': snippet['thumbnails']['high']['url'] if 'high' in snippet['thumbnails'] else snippet['thumbnails']['default']['url'],
                    'publish_date': publish_date.strftime('%Y-%m-%d %H:%M:%S'),
                    'channel_title': snippet['channelTitle'],
                    'channel_key': channel_key,
                    'view_count': details.get('view_count', 'N/A'),
                    'like_count': details.get('like_count', 'N/A'),
                    'duration': self.format_duration(details.get('duration', 'N/A')),
                    'source': 'BYTEBYTEGO'
                }
                
                videos.append(video_info)
            
            return videos
            
        except Exception as e:
            print(f"❌ Error fetching videos for {channel_key}: {e}")
            return []
    
    def get_videos(self, channel_key: str = 'bytebytego') -> List[Dict[str, Any]]:
        """Get videos for a specific channel with caching"""
        cache_key = f"youtube_{channel_key}"
        current_time = time.time()
        
        with self.cache_lock:
            # Check if we have cached data
            if cache_key in self.video_cache:
                last_updated = self.video_cache_last_updated.get(cache_key, 0)
                cache_age = current_time - last_updated
                
                # If cache is fresh (less than 12 hours), return it
                if cache_age < self.cache_fresh_period:
                    return self.video_cache[cache_key]
                
                # If cache is stale but not expired (less than 48 hours), return it
                # Background worker will handle the refresh
                elif cache_age < self.cache_max_period:
                    return self.video_cache[cache_key]
        
        # Check if refresh is already in progress BEFORE starting our own
        refresh_was_in_progress = False
        with self.refresh_lock:
            if channel_key in self.refresh_in_progress:
                refresh_was_in_progress = True
        
        if refresh_was_in_progress:
            # Wait for ongoing refresh to complete (outside of locks to avoid deadlock)
            max_wait_time = 30  # Maximum 30 seconds
            wait_start = time.time()
            while True:
                with self.refresh_lock:
                    if channel_key not in self.refresh_in_progress:
                        break
                
                # Check if we've waited too long
                if time.time() - wait_start > max_wait_time:
                    print(f"⚠️ Timeout waiting for {channel_key} refresh, proceeding anyway...")
                    break
                    
                time.sleep(0.1)  # Wait 100ms before checking again
            
            # Check cache again after waiting - the background refresh should have populated it
            with self.cache_lock:
                if cache_key in self.video_cache:
                    return self.video_cache[cache_key]
        
        # If no cache or cache is too old, and no refresh was in progress, start our own refresh
        success = self._refresh_channel_atomic(channel_key)
        if success:
            with self.cache_lock:
                if cache_key in self.video_cache:
                    return self.video_cache[cache_key]
        
        # If all else fails, return empty list
        return []
    
    def get_all_channels(self) -> List[str]:
        """Get list of all available channel keys"""
        return list(self.channels.keys())
    
    def get_channel_info(self, channel_key: str) -> Dict[str, str]:
        """Get information about a specific channel"""
        return self.channels.get(channel_key, {})
    
    def extract_video_id(self, video_url: str) -> Optional[str]:
        """Extract video ID from YouTube URL"""
        if not video_url:
            return None
            
        if 'youtube.com/watch?v=' in video_url:
            return video_url.split('v=')[1].split('&')[0]
        elif 'youtu.be/' in video_url:
            return video_url.split('youtu.be/')[1].split('?')[0]
        return None
    
    def get_video_transcript(self, video_id: str, use_cache: bool = True) -> Optional[Dict]:
        """
        Extract transcript for a YouTube video using youtube-transcript-api
        
        Args:
            video_id (str): YouTube video ID
            use_cache (bool): Whether to use cached transcript if available
            
        Returns:
            dict: Transcript data with 'success', 'text', 'language', 'length', 'segments' keys
        """
        if not TRANSCRIPT_API_AVAILABLE or not self.transcript_api:
            return {
                'success': False,
                'error': 'youtube-transcript-api not available or not initialized'
            }
        
        # Check cache first if enabled
        if use_cache:
            with self.transcript_cache_lock:
                if video_id in self.transcript_cache:
                    cached_result = self.transcript_cache[video_id]
                    # Check if cache is still fresh (24 hours)
                    if time.time() - cached_result.get('timestamp', 0) < 86400:
                        print(f"Using cached transcript for video {video_id}")
                        return cached_result['data']
        
        try:
            print(f"Extracting transcript for video {video_id}...")
            
            # Use the new API - try to fetch transcript directly first
            transcript_list = None
            used_language = None
            
            try:
                # Try to fetch transcript using the new API
                fetched_transcript = self.transcript_api.fetch(video_id)
                # Extract the transcript list from the FetchedTranscript object
                transcript_list = fetched_transcript.to_raw_data()
                used_language = getattr(fetched_transcript, 'language_code', 'auto-detected')
                print(f"Found transcript using new API in language: {used_language}")
            except RequestBlocked as e:
                print(f"❌ YouTube blocked the request: {str(e)}")
                return {
                    'success': False,
                    'error': f'YouTube blocked the request. This usually happens on cloud platforms. Consider using a proxy. Details: {str(e)}'
                }
            except TranscriptsDisabled as e:
                print(f"❌ Transcripts disabled for video {video_id}: {str(e)}")
                return {
                    'success': False,
                    'error': f'Transcripts are disabled for this video: {str(e)}'
                }
            except Exception as e:
                print(f"Failed to fetch with new API, trying fallback: {str(e)}")
                
                # Fallback to listing transcripts and selecting one
                try:
                    transcript_list_obj = self.transcript_api.list(video_id)
                    
                    # Try to find English transcript first
                    languages = ['en', 'en-US', 'en-GB', 'en-CA', 'en-AU']
                    transcript = None
                    
                    for lang in languages:
                        try:
                            transcript = transcript_list_obj.find_transcript([lang])
                            used_language = lang
                            break
                        except Exception:
                            continue
                    
                    # If no English transcript, get the first available
                    if not transcript:
                        try:
                            transcript = transcript_list_obj.find_transcript()
                            used_language = transcript.language_code if hasattr(transcript, 'language_code') else "unknown"
                        except Exception as find_error:
                            print(f"Could not find any transcript: {str(find_error)}")
                            return {
                                'success': False,
                                'error': f'No transcript available: {str(find_error)}'
                            }
                    
                    # Fetch the transcript content
                    if transcript:
                        fetched_transcript = transcript.fetch()
                        transcript_list = fetched_transcript.to_raw_data()
                        used_language = getattr(fetched_transcript, 'language_code', used_language)
                        print(f"Found transcript in language: {used_language}")
                    
                except RequestBlocked as e:
                    print(f"❌ YouTube blocked the request during fallback: {str(e)}")
                    return {
                        'success': False,
                        'error': f'YouTube blocked the request. This usually happens on cloud platforms. Consider using a proxy. Details: {str(e)}'
                    }
                except Exception as fallback_error:
                    print(f"Fallback method also failed: {str(fallback_error)}")
                    return {
                        'success': False,
                        'error': f'All transcript extraction methods failed: {str(fallback_error)}'
                    }
            
            if not transcript_list:
                result = {
                    'success': False,
                    'error': 'No transcript available for this video'
                }
            else:
                # Validate transcript_list structure
                if not isinstance(transcript_list, list) or len(transcript_list) == 0:
                    result = {
                        'success': False,
                        'error': f'Invalid transcript format: {type(transcript_list)}'
                    }
                else:
                    # Check if transcript entries have the expected structure
                    sample_entry = transcript_list[0]
                    if not isinstance(sample_entry, dict) or 'text' not in sample_entry:
                        result = {
                            'success': False,
                            'error': f'Unexpected transcript entry format: {type(sample_entry)}'
                        }
                    else:
                        # Format the transcript
                        try:
                            formatter = TextFormatter()
                            transcript_text = formatter.format_transcript(transcript_list)
                        except Exception as format_error:
                            # Fallback: manually format the transcript
                            print(f"Formatter failed, using manual formatting: {str(format_error)}")
                            transcript_parts = []
                            for entry in transcript_list:
                                if isinstance(entry, dict) and 'text' in entry:
                                    transcript_parts.append(entry['text'])
                            transcript_text = ' '.join(transcript_parts)
                        
                        # Also get the raw transcript with timestamps for analysis
                        transcript_with_timestamps = []
                        for entry in transcript_list:
                            if isinstance(entry, dict):
                                transcript_with_timestamps.append({
                                    'text': entry.get('text', ''),
                                    'start': entry.get('start', 0),
                                    'duration': entry.get('duration', 0)
                                })
                        
                        print(f"Successfully extracted transcript ({len(transcript_text)} characters)")
                        result = {
                            'success': True,
                            'text': transcript_text,
                            'raw_transcript': transcript_with_timestamps,
                            'length': len(transcript_text),
                            'segments': len(transcript_list),
                            'language': used_language
                        }
            
            # Cache the result
            if use_cache:
                with self.transcript_cache_lock:
                    self.transcript_cache[video_id] = {
                        'data': result,
                        'timestamp': time.time()
                    }
            
            return result
            
        except Exception as e:
            print(f"Error extracting transcript for video {video_id}: {str(e)}")
            result = {
                'success': False,
                'error': str(e)
            }
            
            # Cache failed results too (for a shorter time)
            if use_cache:
                with self.transcript_cache_lock:
                    self.transcript_cache[video_id] = {
                        'data': result,
                        'timestamp': time.time()
                    }
            
            return result
    
    def get_video_content_for_summarization(self, video_id: str, title: str = "") -> str:
        """
        Get comprehensive content for a YouTube video for summarization purposes.
        Focuses on video title and transcript content, skipping description which can be irrelevant.
        
        Args:
            video_id (str): YouTube video ID
            title (str): Video title
            
        Returns:
            str: Formatted content ready for summarization
        """
        content_parts = []
        
        # Add title
        if title:
            content_parts.append(f"Title: {title}")
        
        # Try to get transcript
        transcript_result = self.get_video_transcript(video_id)
        
        if transcript_result and transcript_result.get('success'):
            transcript_text = transcript_result.get('text', '')
            language = transcript_result.get('language', 'unknown')
            
            # Add transcript with metadata
            content_parts.append(f"Transcript (Language: {language}):")
            content_parts.append(transcript_text)
            
            print(f"Using transcript for video {video_id}: {len(transcript_text)} characters")
        else:
            error = transcript_result.get('error', 'Unknown error') if transcript_result else 'No result'
            print(f"No transcript available for video {video_id}: {error}")
            
            # If no transcript, at least indicate it's a video
            content_parts.append("Note: This is a YouTube video. Transcript was not available for detailed analysis.")
        
        # Get additional video details if available
        try:
            video_details = self.get_video_details([video_id])
            if video_id in video_details:
                video_info = video_details[video_id]
                metadata_parts = []
                
                duration = video_info.get('duration', 'N/A')
                if duration != 'N/A':
                    duration = self.format_duration(duration)
                    metadata_parts.append(f"Duration: {duration}")
                
                view_count = video_info.get('view_count', 'N/A')
                if view_count != 'N/A':
                    metadata_parts.append(f"Views: {view_count}")
                
                if metadata_parts:
                    content_parts.append(f"Video Details: {', '.join(metadata_parts)}")
        except Exception as e:
            print(f"Could not fetch video details for {video_id}: {e}")
        
        return '\n\n'.join(content_parts) 