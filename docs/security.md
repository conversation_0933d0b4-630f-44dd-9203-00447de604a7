# Heroku App Security Guide

This document outlines the security measures implemented in the Birdii News Application and provides guidelines for maintaining proper security when deploying to Heroku.

## Implemented Security Features

### HTTPS Enforcement

The application enforces HTTPS in several ways:

1. **Automatic HTTP to HTTPS Redirection**
   ```python
   @main.before_request
   def enforce_https():
       if os.environ.get('DYNO') and request.headers.get('X-Forwarded-Proto') == 'http':
           url = request.url.replace('http://', 'https://', 1)
           return redirect(url, code=301)
   ```
   This middleware intercepts any HTTP request in production and redirects it to HTTPS.

2. **Secure Cookie Settings**
   ```python
   app.config['SESSION_COOKIE_SECURE'] = True
   app.config['SESSION_COOKIE_HTTPONLY'] = True
   app.config['REMEMBER_COOKIE_SECURE'] = True
   ```
   These settings ensure cookies are only transmitted over HTTPS and cannot be accessed via JavaScript.

3. **HTTP Strict Transport Security (HSTS)**
   ```python
   response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
   ```
   This instructs browsers to only connect to the site via HTTPS for one year.

4. **Preferred URL Scheme**
   ```python
   app.config['PREFERRED_URL_SCHEME'] = 'https'
   ```
   This ensures that URLs generated by `url_for()` will use HTTPS by default.

### Security Headers

The application includes several security headers to protect against common web vulnerabilities:

1. **X-Frame-Options**: Prevents clickjacking attacks
2. **X-XSS-Protection**: Helps prevent cross-site scripting attacks
3. **X-Content-Type-Options**: Prevents MIME type sniffing
4. **Referrer-Policy**: Controls how much referrer information is included with requests

## Environment Variables & Secure Configuration

The application uses environment variables for all sensitive configuration:

### Current Environment Variables

| Variable Name | Purpose | Required |
|---------------|---------|----------|
| SECRET_KEY | Flask session encryption | Yes |
| DATABASE_URL | Database connection string | Yes for Heroku |
| NEWS_API_KEY | API key for NewsAPI | Yes |
| OPENAI_API_KEY | API key for OpenAI | Yes |

### Managing Environment Variables on Heroku

To set environment variables securely on Heroku, use the Heroku CLI:

```bash
# Set a single environment variable
heroku config:set SECRET_KEY=your-secret-key

# Set multiple environment variables at once
heroku config:set NEWS_API_KEY=your-api-key OPENAI_API_KEY=your-openai-key

# View current environment variables
heroku config

# Remove an environment variable
heroku config:unset VARIABLE_NAME
```

For local development, use a `.env` file with the same variable names. This file should never be committed to version control.

### Generating a Strong Secret Key

Generate a cryptographically secure secret key with:

```bash
# Using Python
python -c "import secrets; print(secrets.token_hex(32))"

# Using OpenSSL
openssl rand -hex 32
```

## Best Practices for Ongoing Security

1. **Regularly rotate API keys** and update the Heroku config vars
2. **Monitor Heroku logs** for suspicious activity:
   ```bash
   heroku logs --tail
   ```
3. **Keep dependencies updated** to patch security vulnerabilities
4. **Enable Heroku dyno metadata** for better security management:
   ```bash
   heroku labs:enable runtime-dyno-metadata
   ```
5. **Set up Heroku's free SSL certificates** for your custom domain if applicable

## Further Security Enhancements (Recommended)

For enhanced security, consider implementing:

1. **Rate limiting** to prevent abuse
2. **Content Security Policy (CSP)** to control which resources can be loaded
3. **Cross-Origin Resource Sharing (CORS)** policies
4. **Request validation** for all user inputs
5. **Regular security audits** with tools like OWASP ZAP 