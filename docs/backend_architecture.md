# Birdii News Application - Backend Architecture

## Table of Contents
1. [System Overview](#system-overview)
2. [News Fetching Strategy](#news-fetching-strategy)
3. [Caching Architecture](#caching-architecture)
4. [Background Refresh Mechanism](#background-refresh-mechanism)
5. [API Quota Management](#api-quota-management)
6. [Error Handling & Resilience](#error-handling--resilience)
7. [Performance Optimizations](#performance-optimizations)

## System Overview

The Birdii News Application backend is built on Flask and provides a RESTful API for serving news articles, article summaries, background context, and interactive Q&A capabilities. The core components include:

- **News Service**: Handles fetching, caching, and refreshing of news articles from NewsAPI
- **Summarizer**: Extracts and summarizes article content using AI
- **API Routes**: Endpoints for client interaction with proper caching and error handling
- **Background Workers**: Asynchronous workers for refreshing content and generating summaries

The architecture is designed to optimize for:
- Reduced API calls to external services
- Fast response times for users
- Content freshness
- High availability and resilience

## News Fetching Strategy

The news fetching logic is implemented in the `NewsService` class and follows these key strategies:

### Source Selection

```python
def _fetch_from_api(self, topic):
    # Prioritize BBC News and Reuters by always including them when available
    priority_sources = ['bbc-news', 'reuters']
    
    # Randomly select additional sources to reach 5 total
    remaining_sources = [s for s in self.premium_sources if s not in priority_sources]
    additional_sources = random.sample(remaining_sources, min(5, len(remaining_sources)))
    
    # Combine priority and additional sources
    selected_sources = priority_sources + additional_sources
    sources_param = ','.join(selected_sources)
```

The system maintains a list of premium news sources and uses a hybrid strategy:
1. **Priority Sources**: BBC News and Reuters are always included when available
2. **Randomized Selection**: Additional sources are randomly selected to complete the set
3. **Fallback Mechanism**: If insufficient articles are found, the query is broadened

### Topic Mapping

```python
def map_topic_to_query(self, topic):
    topic_lower = topic.lower()
    
    if topic_lower == 'science & technology':
        return "Science OR Technology OR innovation OR breakthrough OR discovery OR research"
    elif topic_lower == 'politics':
        return "Politics OR government OR policy OR election"
    # Additional mappings...
```

Frontend topic selections are mapped to optimized search queries for NewsAPI to ensure:
- Broad coverage of relevant articles
- Better semantic matching than direct keyword searches
- Consistent results across multiple news sources

### Source Diversity

The system ensures articles come from diverse sources to prevent single-source dominance:

```python
def ensure_source_diversity(self, response, priority_sources=None):
    # Group articles by source
    priority_articles = []
    other_articles = []
    
    # Rearrange articles to alternate between priority and other sources
    # ...
```

This creates a balanced mix of high-quality sources while still prioritizing trusted outlets.

## Caching Architecture

The application implements a sophisticated multi-tier caching strategy:

### In-Memory Cache for News Articles

```python
# News cache for storing pre-fetched news
self.news_cache = {}
self.news_cache_last_updated = {}
self.cache_lock = threading.Lock()
```

News articles are cached in memory with thread-safe operations protected by a lock to prevent race conditions.

### Cache Versioning

```python
# Cache version - increment when schema changes
self.CACHE_VERSION = 1

def _create_cache_key(self, topic):
    # Include cache version and date in the key for versioning
    today = datetime.now().strftime('%Y%m%d')
    return f"newsv{self.CACHE_VERSION}_{today}_{topic.lower().replace(' & ', '_').replace(' ', '_')}"
```

The caching system includes version control to:
- Facilitate schema changes without breaking existing caches
- Enable date-based cache namespacing
- Support clean cache invalidation when needed

### Flask-Caching for API Endpoints

```python
@main.route('/news')
@cache.cached(timeout=3600, query_string=True)  # Cache results for 1 hour based on the query string
def get_news():
    # Implementation...
```

The `/news` endpoint is wrapped with Flask-Caching to provide HTTP-level caching for 1 hour, keyed by the query string parameters. This reduces database queries and processing overhead.

### Tiered Cache Freshness Policy

The news service implements a tiered approach to cache freshness:

```python
# Check if cache is valid (less than 30 minutes old)
current_time = time.time()
cache_age = current_time - last_updated

if cached_data and cache_age < 1800:  # 30 minutes
    print(f"Cache hit for topic: {topic}")
    return cached_data
    
# If cache exists but is stale (>30 min but <2 hours), schedule refresh but return cache
if cached_data and cache_age < 7200:  # 2 hours
    print(f"Returning stale cache for topic: {topic}, will refresh in background")
    # Trigger an async refresh
    threading.Thread(target=self._refresh_topic, args=(topic,), daemon=True).start()
    return cached_data
```

This creates a 3-tier freshness policy:
1. **Fresh Cache** (<30 minutes old): Return immediately
2. **Stale Cache** (30 min - 2 hours): Return cache but trigger async refresh
3. **Expired Cache** (>2 hours): Fetch new data unless API limit is reached

## Background Refresh Mechanism

The application employs a proactive background refresh strategy to ensure content is always fresh without blocking user requests:

### Background Worker Thread

```python
def start_background_refresh(self):
    """Start a background thread to periodically refresh news for popular topics"""
    refresh_thread = threading.Thread(target=self._background_refresh_worker, daemon=True)
    refresh_thread.start()
```

A daemon thread runs in the background, continuously refreshing the news cache for popular topics.

### Initial Cache Warm-up

```python
# Initial warm-up for all popular topics
print("Performing initial cache warm-up...")
for topic in self.popular_topics:
    try:
        print(f"Warming up cache for topic: {topic}")
        self._refresh_topic(topic)
        # Sleep between topics to avoid API rate limiting
        time.sleep(5)
    except Exception as e:
        print(f"Error in cache warm-up for topic {topic}: {str(e)}")
```

On application startup, the system performs an initial cache warm-up for all popular topics to ensure fast response times for initial users.

### Periodic Refresh Cycles

```python
# Sleep for 30 minutes before next refresh cycle
time.sleep(1800)
```

The background worker refreshes all popular topics every 30 minutes, ensuring content is regularly updated without exceeding API quotas.

### On-Demand Async Refreshes

```python
# Trigger an async refresh
threading.Thread(target=self._refresh_topic, args=(topic,), daemon=True).start()
```

When a stale cache is accessed, the system triggers an asynchronous refresh to update the cache for subsequent requests without blocking the current request.

## API Quota Management

The system implements sophisticated API quota management to prevent exceeding NewsAPI rate limits:

### Daily Counter with Automatic Reset

```python
def _reset_api_counter_if_needed(self):
    """Reset the API call counter if it's a new day"""
    current_date = datetime.now().date()
    
    with self.api_quota_lock:
        if current_date > self.api_calls_reset_date:
            print(f"Resetting API call counter. Previous count: {self.api_calls_today}")
            self.api_calls_today = 0
            self.api_calls_reset_date = current_date
```

The counter automatically resets at midnight to align with NewsAPI's daily quota cycle.

### Threshold Warnings

```python
def _increment_api_counter(self):
    """Increment the API call counter"""
    with self.api_quota_lock:
        self.api_calls_today += 1
        # Log when we hit 50% and 80% of our limit
        if self.api_calls_today == int(self.API_DAILY_LIMIT * 0.5):
            print(f"⚠️ WARNING: Reached 50% of API call limit ({self.api_calls_today}/{self.API_DAILY_LIMIT})")
        elif self.api_calls_today == int(self.API_DAILY_LIMIT * 0.8):
            print(f"⚠️ WARNING: Reached 80% of API call limit ({self.api_calls_today}/{self.API_DAILY_LIMIT})")
        elif self.api_calls_today >= self.API_DAILY_LIMIT:
            print(f"🛑 CRITICAL: Exceeded API call limit ({self.api_calls_today}/{self.API_DAILY_LIMIT})")
```

The system logs warnings at 50% and 80% of the daily limit to provide visibility into API usage.

### Adaptive Behavior Near Limits

```python
# If cache doesn't exist or is too old, check API quota before fetching
if self._is_near_api_limit():
    print(f"WARNING: Near API limit, using stale cache for topic: {topic}")
    if cached_data:
        return cached_data
    
    # If no cache at all and near limit, return first available cached topic or empty
    with self.cache_lock:
        if self.news_cache:
            # Return any cached topic as fallback
            print(f"WARNING: No cache for {topic} and near API limit, returning fallback cached topic")
            return next(iter(self.news_cache.values()))
        else:
            print(f"WARNING: No cache available and near API limit, returning empty response")
            return {"articles": []}
```

When approaching the API limit:
1. The system prioritizes using cached data, even if stale
2. Background refreshes are paused to preserve quota for user-initiated requests
3. As a last resort, the system will serve content from a different topic when the requested topic has no cache

## Error Handling & Resilience

The application implements comprehensive error handling to ensure continuous operation:

### Resilient API Calls

```python
try:
    # First try with priority sources and date range
    if search_query:
        response = self.newsapi.get_everything(...)
    else:
        response = self.newsapi.get_top_headlines(...)
    
    # Check if we got enough articles
    if not response.get('articles') or len(response.get('articles', [])) < 10:
        # Try with a broader query without source restrictions
        # ...
        
except Exception as source_error:
    print(f"Error with source-specific query: {source_error}. Trying general query.")
    # Fallback logic...
```

The news fetching includes multiple fallback strategies:
1. First attempts with specific sources and parameters
2. If insufficient articles are found, broadens the query
3. If an error occurs, falls back to a more general query

### Cache-Based Fallbacks

```python
except Exception as e:
    print(f"Error in get_articles: {str(e)}")
    # If any error occurs, attempt to use cached data even if stale
    with self.cache_lock:
        cached_data = self.news_cache.get(self._create_cache_key(topic))
    
    # Return cached data as fallback or empty response
    return cached_data if cached_data else {"articles": []}
```

If all API calls fail, the system will:
1. Return cached data even if expired
2. As a last resort, return an empty article list rather than an error

### Background Thread Error Recovery

```python
except Exception as e:
    print(f"Error in background refresh thread: {str(e)}")
    # Sleep for 5 minutes on error before retrying
    time.sleep(300)
```

The background refresh thread includes error recovery to prevent a single error from permanently stopping the refresh cycle.

## Performance Optimizations

Beyond the core caching and fetching strategies, the application includes additional optimizations:

### Thread-Safe Operations

```python
# Thread safety with locks
self.cache_lock = threading.Lock()
self.api_quota_lock = threading.Lock()

# Usage
with self.cache_lock:
    self.news_cache[cache_key] = api_response
    self.news_cache_last_updated[cache_key] = time.time()
```

All shared resources are protected by locks to ensure thread safety in the multithreaded environment.

### Staggered Refresh Timing

```python
# Sleep between topics to avoid API rate limiting
time.sleep(5)
```

Refresh operations are staggered with small delays between topics to prevent API rate limiting and distribute system load.

### Response Processing

The `/news` endpoint includes sophisticated response processing:

```python
# First sort by date (latest first)
processed_articles.sort(key=lambda x: x.get('published_at_raw', ''), reverse=True)

# Then prioritize today's news by moving them to the top while maintaining their original order
today_articles = [article for article in processed_articles if article.get('is_today', False)]
older_articles = [article for article in processed_articles if not article.get('is_today', False)]

# Combine the lists with today's articles first
prioritized_articles = today_articles + older_articles
```

This ensures the most relevant and recent news is displayed first, while still maintaining chronological ordering within each group.

### Article Description Formatting

The system includes comprehensive formatting for article descriptions:

```python
def format_description(description):
    """
    Ensure article descriptions are complete sentences with proper formatting.
    """
    # Extensive formatting logic...
```

This improves the user experience by ensuring consistent, well-formatted article descriptions without HTML entities, encoding issues, or incomplete sentences. 