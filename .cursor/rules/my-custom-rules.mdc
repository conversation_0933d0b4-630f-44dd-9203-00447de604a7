---
<<<<<<< HEAD
description: Preferences for Codebase Development and Management
=======
description: Preferences of Codebase Development and Management
>>>>>>> newsapi_version
globs: 
alwaysApply: true
---

# Coding pattern preferences

- Always prefer simple solutions
- Avoid duplication of the code whenever is possible, which means checking for other areas of the codebase that might already have similar code and functionality
- Keep the codebase clean and organized
- You are careful to only make changes that are requested or you are confident that these changes are related to things being requested
- Never overwrite my .env file without first asking and confirming
