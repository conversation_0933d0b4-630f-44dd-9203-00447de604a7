from app import create_app, db
from app.models import Admin
from sqlalchemy import inspect
from getpass import getpass

app = create_app()

def create_admin_user():
    """Create an admin user for the application."""
    with app.app_context():
        # Check if admin table exists
        inspector = inspect(db.engine)
        if not inspector.has_table('admin'):
            print("Creating database tables...")
            db.create_all()
        
        print("Create Admin User")
        print("-----------------")
        
        # Set default admin credentials for simplicity
        username = "admin"
        password = "admin123"
        
        # Check if admin already exists
        existing_admin = Admin.query.filter_by(username=username).first()
        if existing_admin:
            print(f"Admin user '{username}' already exists.")
            print(f"Resetting password to default: '{password}'")
            
            existing_admin.set_password(password)
            db.session.commit()
            print(f"Password for admin user '{username}' has been updated.")
            return
        
        # Create new admin with default credentials
        admin = Admin(username=username)
        admin.set_password(password)
        
        db.session.add(admin)
        db.session.commit()
        
        print(f"Admin user '{username}' has been created successfully with password '{password}'.")
        print("IMPORTANT: Change this password after first login for security reasons.")

if __name__ == "__main__":
    create_admin_user()
