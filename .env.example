# Flask Application Settings
SECRET_KEY=generate-a-secure-random-key-for-production

# Database Configuration
# For local development
# DATABASE_URL=sqlite:///news.db
# For Heroku PostgreSQL
# DATABASE_URL=postgresql://username:password@localhost/dbname

# API Keys (required)
NEWS_API_KEY=your-newsdata-io-key
OPENAI_API_KEY=your-openai-api-key

# Cache Configuration (optional)
# CACHE_TYPE=SimpleCache
# For production, consider Redis
# CACHE_TYPE=RedisCache
# CACHE_REDIS_URL=redis://localhost:6379/0

# Debug Settings (remove in production)
# FLASK_ENV=development
# FLASK_DEBUG=1

# You can get an OpenAI API key at: https://platform.openai.com/api-keys
# You can get a NewsData.io API key at: https://newsdata.io/register

# Email Configuration for SendGrid
MAIL_SERVER=smtp.sendgrid.net
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=apikey
MAIL_PASSWORD=your-sendgrid-api-key
SENDGRID_API_KEY=your-sendgrid-api-key
MAIL_DEFAULT_SENDER="Your App Name <<EMAIL>>"

# Custom domain for URL generation in emails
CUSTOM_DOMAIN=www.yourdomain.com
PREFERRED_URL_SCHEME=https

# reCAPTCHA Configuration
# Get your reCAPTCHA keys at: https://www.google.com/recaptcha/admin
RECAPTCHA_SITE_KEY=your-recaptcha-site-key
RECAPTCHA_SECRET_KEY=your-recaptcha-secret-key