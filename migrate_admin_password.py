from app import create_app, db
from sqlalchemy import text

app = create_app()

def migrate_admin_password_column():
    """Alter the admin.password_hash column to increase its size."""
    with app.app_context():
        try:
            # Check if the admin table exists
            with db.engine.connect() as conn:
                # First, check if the table exists
                result = conn.execute(text("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'admin')"))
                table_exists = result.scalar()
                
                if not table_exists:
                    print("Admin table doesn't exist yet. No migration needed.")
                    return
                
                # Alter the column size
                print("Altering admin.password_hash column size to 256 characters...")
                conn.execute(text("ALTER TABLE admin ALTER COLUMN password_hash TYPE VARCHAR(256)"))
                conn.commit()
                print("Column size successfully updated.")
        except Exception as e:
            print(f"Error during migration: {e}")
            raise

if __name__ == "__main__":
    migrate_admin_password_column()
