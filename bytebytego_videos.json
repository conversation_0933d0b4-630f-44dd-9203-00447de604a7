[{"title": "APIs Explained in 6 Minutes!", "description": "Sign up now to access ChatLLM: https://bit.ly/42RlGDV\n\nGet a Free System Design PDF with 158 pages by subscribing to our weekly newsletter: https://bit.ly/bbg-social\n\nAnimation tools: Adobe Illustrator and After Effects.\n\nCheckout our bestselling System Design Interview books: \nVolume 1: https://amzn.to/3Ou7gkd\nVolume 2: https://amzn.to/3HqGozy\n\nThe digital version of System Design Interview books: https://bit.ly/3mlDSk9\n\nABOUT US: \nCovering topics and trends in large-scale system design, from the authors of the best-selling System Design Interview series.", "video_id": "hltLrjabkiY", "video_url": "https://www.youtube.com/watch?v=hltLrjabkiY", "thumbnail_url": "https://i.ytimg.com/vi/hltLrjabkiY/hqdefault.jpg", "publish_date": "2025-05-15 15:30:20", "channel_title": "ByteByteGo", "view_count": "37510", "like_count": "1652", "duration": "6:41"}, {"title": "Why is <PERSON><PERSON><PERSON> so Fast? Part 2", "description": "Get our 158-page System Design PDF for free by subscribing to our weekly newsletter: https://bit.ly/bytebytegoYTshorts\n\nAnimation tools: Adobe Illustrator and After Effects.\n\nCheckout our bestselling System Design Interview books: \nVolume 1: https://amzn.to/3Ou7gkd\nVolume 2: https://amzn.to/3HqGozy\n\nThe digital version of System Design Interview books: https://bit.ly/3mlDSk9\n\nABOUT US: \nCovering topics and trends in large-scale system design, from the authors of the best-selling System Design Interview series.", "video_id": "la8tzEyg-hY", "video_url": "https://www.youtube.com/watch?v=la8tzEyg-hY", "thumbnail_url": "https://i.ytimg.com/vi/la8tzEyg-hY/hqdefault.jpg", "publish_date": "2025-05-12 15:01:14", "channel_title": "ByteByteGo", "view_count": "41437", "like_count": "1494", "duration": "1:45"}, {"title": "Why is Kafka FAST? Part 1", "description": "", "video_id": "wvLdBJEl-wc", "video_url": "https://www.youtube.com/watch?v=wvLdBJEl-wc", "thumbnail_url": "https://i.ytimg.com/vi/wvLdBJEl-wc/hqdefault.jpg", "publish_date": "2025-05-08 14:45:59", "channel_title": "ByteByteGo", "view_count": "65467", "like_count": "2021", "duration": "2:05"}, {"title": "System Design Was HARD - Until You Knew the Trade-Offs, Part 2", "description": "Sign up now to access ChatLLM: https://bit.ly/42RlGDV\n\nGet a Free System Design PDF with 158 pages by subscribing to our weekly newsletter: https://bit.ly/bbg-social\n\nAnimation tools: Adobe Illustrator and After Effects.\n\nCheckout our bestselling System Design Interview books: \nVolume 1: https://amzn.to/3Ou7gkd\nVolume 2: https://amzn.to/3HqGozy\n\nThe digital version of System Design Interview books: https://bit.ly/3mlDSk9\n\nABOUT US: \nCovering topics and trends in large-scale system design, from the authors of the best-selling System Design Interview series.", "video_id": "2g1G8Jr88xU", "video_url": "https://www.youtube.com/watch?v=2g1G8Jr88xU", "thumbnail_url": "https://i.ytimg.com/vi/2g1G8Jr88xU/hqdefault.jpg", "publish_date": "2025-04-30 15:30:03", "channel_title": "ByteByteGo", "view_count": "33334", "like_count": "1340", "duration": "6:12"}, {"title": "System Design Was HARD - Until You Knew the Trade-Offs", "description": "Get a Free System Design PDF with 158 pages by subscribing to our weekly newsletter: https://bit.ly/bbg-social\n\nAnimation tools: Adobe Illustrator and After Effects.\n\nCheckout our bestselling System Design Interview books: \nVolume 1: https://amzn.to/3Ou7gkd\nVolume 2: https://amzn.to/3HqGozy\n\nThe digital version of System Design Interview books: https://bit.ly/3mlDSk9\n\nABOUT US: \nCovering topics and trends in large-scale system design, from the authors of the best-selling System Design Interview series.", "video_id": "1nENigGr-a0", "video_url": "https://www.youtube.com/watch?v=1nENigGr-a0", "thumbnail_url": "https://i.ytimg.com/vi/1nENigGr-a0/hqdefault.jpg", "publish_date": "2025-04-22 15:30:00", "channel_title": "ByteByteGo", "view_count": "91661", "like_count": "3773", "duration": "5:09"}, {"title": "gRPC, WebSocket and WebHook!", "description": "", "video_id": "f7KgDNZU3-Y", "video_url": "https://www.youtube.com/watch?v=f7KgDNZU3-Y", "thumbnail_url": "https://i.ytimg.com/vi/f7KgDNZU3-Y/hqdefault.jpg", "publish_date": "2025-04-18 05:17:15", "channel_title": "ByteByteGo", "view_count": "128318", "like_count": "4353", "duration": "0:52"}, {"title": "Why Everyone’s Talking About MCP?", "description": "Fixed 2:44–3:10 in the video uploaded to X (we’re unable to edit videos that are already uploaded to YouTube):  https://x.com/bytebytego/status/1907838355657863385\n\nGet a Free System Design PDF with 158 pages by subscribing to our weekly newsletter: https://bit.ly/bbg-social\n\nAnimation tools: Adobe Illustrator and After Effects.\n\nCheckout our bestselling System Design Interview books: \nVolume 1: https://amzn.to/3Ou7gkd\nVolume 2: https://amzn.to/3HqGozy\n\nThe digital version of System Design Interview books: https://bit.ly/3mlDSk9\n\nABOUT US: \nCovering topics and trends in large-scale system design, from the authors of the best-selling System Design Interview series.", "video_id": "_d0duu3dED4", "video_url": "https://www.youtube.com/watch?v=_d0duu3dED4", "thumbnail_url": "https://i.ytimg.com/vi/_d0duu3dED4/hqdefault.jpg", "publish_date": "2025-04-02 15:30:14", "channel_title": "ByteByteGo", "view_count": "250304", "like_count": "6884", "duration": "5:03"}, {"title": "Top 6 Most Popular API Architecture Styles", "description": "", "video_id": "PNRbanEKGtw", "video_url": "https://www.youtube.com/watch?v=PNRbanEKGtw", "thumbnail_url": "https://i.ytimg.com/vi/PNRbanEKGtw/hqdefault.jpg", "publish_date": "2025-03-26 15:30:10", "channel_title": "ByteByteGo", "view_count": "101912", "like_count": "3725", "duration": "1:14"}, {"title": "What Are AI Agents Really About?", "description": "Get a Free System Design PDF with 158 pages by subscribing to our weekly newsletter: https://bit.ly/bbg-social\n\nAnimation tools: Adobe Illustrator and After Effects.\n\nCheckout our bestselling System Design Interview books: \nVolume 1: https://amzn.to/3Ou7gkd\nVolume 2: https://amzn.to/3HqGozy\n\nThe digital version of System Design Interview books: https://bit.ly/3mlDSk9\n\nABOUT US: \nCovering topics and trends in large-scale system design, from the authors of the best-selling System Design Interview series.", "video_id": "eHEHE2fpnWQ", "video_url": "https://www.youtube.com/watch?v=eHEHE2fpnWQ", "thumbnail_url": "https://i.ytimg.com/vi/eHEHE2fpnWQ/hqdefault.jpg", "publish_date": "2025-03-19 15:30:14", "channel_title": "ByteByteGo", "view_count": "144606", "like_count": "3655", "duration": "5:32"}, {"title": "What is GraphQL?", "description": "GraphQL \n- Provides a single endpoint for clients to query for precisely the data they need. \n- Clients specify the exact fields required in nested queries, and the server returns optimized payloads containing just those fields. \n- Supports Mutations for modifying data and Subscriptions for real-time notifications. \n- Great for aggregating data from multiple sources and works well with rapidly evolving frontend requirements. \n- However, it shifts complexity to the client side and can allow abusive queries if not properly safeguarded \n- Caching strategies can be more complicated than REST.", "video_id": "rQhost93z40", "video_url": "https://www.youtube.com/watch?v=rQhost93z40", "thumbnail_url": "https://i.ytimg.com/vi/rQhost93z40/hqdefault.jpg", "publish_date": "2025-03-10 14:55:18", "channel_title": "ByteByteGo", "view_count": "103446", "like_count": "5088", "duration": "0:42"}]