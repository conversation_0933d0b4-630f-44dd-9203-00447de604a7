# News Summarizer App

A Flask application that aggregates news articles from NewsData.io and uses OpenAI to generate personalized summaries based on user interests, enhanced with real-time web search capabilities.

## Features

- Fetch latest news articles from various topics
- Generate AI-powered summaries of news articles using OpenAI
- Filter news by topic
- Store articles and summaries in a database
- **Real-time web search integration** for Additional Context and user questions
- Deployable to <PERSON>ku with one click

## Requirements

- Python 3.11+
- OpenAI API key
- NewsData.io API key

## Installation

1. Clone this repository:
```bash
git clone <repository-url>
cd news-summarizer
```

2. Create a virtual environment:
```bash
# Using conda
conda create --name news_app python=3.11 pip -y
conda activate news_app

# Or using venv
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Create a `.env` file in the root directory with your API keys:
```
OPENAI_API_KEY=your_openai_api_key_here
NEWS_DATA_API_KEY=your_newsdata_io_api_key_here
```

## Running the App

```bash
python run.py
```

The app will be available at http://localhost:5000

## Deploying to Heroku

### Prerequisites
- [Heroku CLI](https://devcenter.heroku.com/articles/heroku-cli) installed
- Heroku account
- Git installed

### Deployment Steps

1. Login to Heroku:
```bash
heroku login
```

2. Create a new Heroku app:
```bash
heroku create your-news-summarizer-app
```

3. Provision a PostgreSQL database:
```bash
heroku addons:create heroku-postgresql:mini
```

4. Set your environment variables:
```bash
heroku config:set OPENAI_API_KEY=your_openai_api_key_here
heroku config:set NEWS_DATA_API_KEY=your_newsdata_io_api_key_here
heroku config:set SECRET_KEY=your_secret_key_here
```

5. Push your code to Heroku:
```bash
git push heroku main
```

6. Initialize the database:
```bash
heroku run python init_db.py
```

7. Open your app:
```bash
heroku open
```

### Troubleshooting

- If you encounter any issues with dependencies, make sure your `requirements.txt` file is complete
- For database issues, check the logs using `heroku logs --tail`
- If the app crashes on startup, ensure all environment variables are set correctly

## Project Structure

```
news-summarizer/
├── .env                # Environment variables (API keys)
├── .gitignore         # Git ignore file
├── config.py          # Configuration file
├── Procfile           # Heroku deployment configuration
├── runtime.txt        # Python version for Heroku
├── requirements.txt   # Python dependencies
├── run.py             # Entry point
├── init_db.py         # Database initialization script
└── app/
    ├── __init__.py    # App initialization
    ├── models.py      # Database models
    ├── news_service.py # NewsData.io integration
    ├── routes.py      # Flask routes
    ├── summarizer.py  # Article summarizer
    └── templates/
        ├── base.html  # Base template
        └── index.html # Main page template
```

## How It Works

1. The app fetches news articles from NewsData.io based on the selected topic
2. For each article, it uses OpenAI to generate a concise summary
3. The summaries are stored in a database and displayed to the user
4. Users can filter articles by topic
5. When users click "Additional Context", the app uses OpenAI's web search to provide enriched background information
6. Users can ask any question about an article using the "Ask Anything" feature, which also leverages OpenAI's web search for up-to-date responses

## Web Search Integration

The app features OpenAI's web search integration in two key areas:

1. **Additional Context Generation**:
   - Combines article data from newspaper3k extraction with real-time web search results
   - Provides comprehensive background information with citations to sources
   - Enhances user understanding with latest relevant information about the topic

2. **Ask Anything Feature**:
   - Users can ask any question related to the article or general topics
   - Answers are generated using OpenAI's web search to ensure up-to-date information
   - Includes citations to reliable sources for fact-checking
   - Provides a seamless way to explore related topics with factual backing

## License

MIT
